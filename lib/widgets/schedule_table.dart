import 'package:flutter/material.dart';
import 'package:koushuu_system/modals/reservation.dart';
import 'package:koushuu_system/modals/term.dart';
import 'package:koushuu_system/models/course.dart';

class ScheduleTable extends StatelessWidget {
  final String title;
  final String kaijo;
  final String koushuuCode;
  final String koushuuNumber;
  final String jimusyoCode;
  final String jimusyoName;
  final String koushuuName;
  final List<AttendDay> body; // List of entries with date and status

  const ScheduleTable({
    super.key,
    required this.title,
    required this.body,
    required this.kaijo,
    required this.koushuuCode,
    required this.koushuuNumber,
    required this.jimusyoCode,
    required this.jimusyoName,
    required this.koushuuName,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Title section with blue background
        Container(
          color: Colors.green,
          padding: const EdgeInsets.all(8.0),
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: const TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
        ),
        // Body section with dates and statuses
        ...body.map((entry) => Container(
              color: Colors.blue[50],
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    flex: 5,
                    child: Text(
                      "${entry.start_date} ~ ${entry.end_date}",
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 20),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      entry.course_name,
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 20),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: InkWell(
                      hoverColor: Colors.green.shade400,
                      onTap: () async {
                        if (entry.suspended_flag == 0 &&
                            (entry.capacity_count - entry.request_count) > 0) {
                          final reDlg = await showModalBottomSheet(
                            isDismissible: false,
                            useSafeArea: true,
                            isScrollControlled: true,
                            context: context,
                            builder: (ctx) => TermPage(),
                          );

                          if (reDlg == null || !reDlg) {
                            return;
                          }

                          await showModalBottomSheet(
                            useSafeArea: true,
                            isScrollControlled: true,
                            isDismissible: false,
                            enableDrag: false,
                            context: context,
                            builder: (ctx) => ReservationPage(
                              title: title,
                              startDate: entry.start_date,
                              endDate: entry.end_date,
                              mancnt:
                                  entry.capacity_count - entry.request_count,
                              kaijo: kaijo,
                              courseHour: entry.course_name,
                              price: entry.price.toString(),
                              koushuuCode: koushuuCode,
                              koushuuNumber: koushuuNumber,
                              courseCode: entry.course_code,
                              jimusyoCode: jimusyoCode,
                              jimusyoName: jimusyoName,
                              koushuuName: koushuuName,
                            ),
                          );
                        }
                      },
                      child: Text(
                        (entry.suspended_flag == 1 ||
                                (entry.capacity_count - entry.request_count) <
                                    1)
                            ? "✖️"
                            : "◯",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 20,
                          color: (entry.suspended_flag == 1 ||
                                  (entry.capacity_count - entry.request_count) <
                                      1)
                              ? Colors.black
                              : Colors.green,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            )),
      ],
    );
  }
}
