// presentation/location_ui.dart
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:koushuu_system/repositories/pref_repository.dart';

final prefValueProvider = StateProvider<String?>((ref) => null);

// ignore: must_be_immutable
class PrefListUi extends ConsumerWidget {
  const PrefListUi(this.name, {super.key});

  final String name;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final fetchAsync = ref.watch(fetchPrefProvider);
    final selectValue = ref.watch(prefValueProvider);

    return fetchAsync.when(
      data: (lst) => FormBuilderDropdown<String>(
        name: name,
        initialValue: selectValue,
        validator: (value) {
          if (value == null || value.isEmpty) {
            return '事務所を選択してください。';
          }
          return null;
        },
        onChanged: (String? newValue) {
          ref.read(prefValueProvider.notifier).state = newValue!;
        },
        items: lst.map((it) {
          return DropdownMenuItem<String>(
            value: it.jimusyo_code,
            child: Text(it.jimusyo_name),
          );
        }).toList(),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (e, _) =>
          const Center(child: Text("事務所情報見つかりませんでした。")), //e.toString()),
    );
  }
}
