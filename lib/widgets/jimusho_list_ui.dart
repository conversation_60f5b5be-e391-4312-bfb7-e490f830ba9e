import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:koushuu_system/repositories/location_repository.dart';
import 'package:koushuu_system/widgets/error_info.dart';

final jimushoValueProvider = StateProvider<String?>((ref) => null);

class JimushoListUi extends ConsumerWidget {
  const JimushoListUi({super.key, this.name = "jimusho"});

  final String name;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // note
    final fetchAsync = ref.watch(fetchLocationProvider);
    final selectValue = ref.watch(jimushoValueProvider);

    return fetchAsync.when(
      data: (val) => FormBuilderDropdown<String>(
        name: name,
        initialValue: selectValue,
        onChanged: (String? newValue) {
          ref.read(jimushoValueProvider.notifier).state = newValue!;
        },
        items: val.map((loc) {
          return DropdownMenuItem<String>(
            value: loc.kaijyo_code,
            child: Text(loc.kaijyo_name),
          );
        }).toList(),
        isExpanded: true,
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (e, _) => ErrorInfo(txt: e.toString()),
    );
  }
}
