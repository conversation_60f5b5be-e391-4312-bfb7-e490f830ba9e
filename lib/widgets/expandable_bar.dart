import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:koushuu_system/modals/koushuu_desc.dart';
import 'package:koushuu_system/models/schedule_data.dart';
import 'package:koushuu_system/widgets/schedule_table.dart';

class ExpandableBar extends StatefulWidget {
  const ExpandableBar({
    super.key,
    required this.title,
    required this.koushuuCode,
    required this.koushuuNumber,
    required this.titleDesc,
    required this.scheduleData,
    required this.jimusyoCode,
    required this.jimusyoName,
    required this.kaijo,
    required this.koushuuName,
  });

  final String title;
  final String koushuuCode;
  final String koushuuNumber;
  final String titleDesc;
  final String jimusyoCode;
  final String jimusyoName;
  final String kaijo;
  final String koushuuName;
  final List<ScheduleData> scheduleData;
  @override
  State<ExpandableBar> createState() => _ExpandableBarState();
}

class _ExpandableBarState extends State<ExpandableBar>
    with TickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ExpandablePanel(
      header: Padding(
        padding: const EdgeInsets.only(right: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                widget.title,
                style: const TextStyle(fontSize: 20),
                textAlign: TextAlign.left,
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(top: 3),
              child: ElevatedButton(
                onPressed: () {
                  showModalBottomSheet(
                    useSafeArea: true,
                    isScrollControlled: true,
                    context: context,
                    builder: (ctx) => KoushouDescriptionPage(
                      title: widget.titleDesc,
                      koushuu_code: widget.koushuuCode,
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.greenAccent.shade700,
                  elevation: 0,
                ),
                child: const Text(
                  "講習説明",
                  style: TextStyle(fontSize: 16, color: Colors.white),
                ),
              ),
            ),
          ],
        ),
      ),
      collapsed: Container(),
      expanded: Column(
        children: [
          for (var item in widget.scheduleData)
            ScheduleTable(
              title: item.month,
              body: item.attend_days,
              kaijo: widget.kaijo,
              koushuuCode: widget.koushuuCode,
              koushuuNumber: widget.koushuuNumber,
              jimusyoCode: widget.jimusyoCode,
              jimusyoName: widget.jimusyoName,
              koushuuName: widget.koushuuName,
            ),
          InkWell(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Container(
                  height: 40,
                  alignment: Alignment.center,
                  color: Colors.greenAccent.shade700,
                  child: const Text(
                    "受講条件確認",
                    style: TextStyle(fontSize: 16, color: Colors.white),
                  ),
                ),
              ),
              onTap: () {
                _dialogBuilder(context);
              }),
        ],
      ),
    );
  }
}

Future<void> _dialogBuilder(BuildContext context) {
  return showDialog<void>(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: const Text('小型移動式クレーン運転抆能講習'),
        content: const Text(
          '・クレーン・デリック運転土免許を有する方\n'
          '・床上操作式クレーン運転技能講習を修了された方\n'
          '・揚貨装置運転士免許を存する方\n'
          '・玉掛け技能講習を了された方',
          style: TextStyle(fontSize: 16),
        ),
        actions: <Widget>[
          TextButton(
            style: TextButton.styleFrom(
              textStyle: Theme.of(context).textTheme.labelLarge,
            ),
            child: const Text('閉じる'),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ],
      );
    },
  );
}
