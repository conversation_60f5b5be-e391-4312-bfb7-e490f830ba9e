// presentation/location_ui.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:koushuu_system/repositories/location_repository.dart';

final locationValueProvider = StateProvider<String?>((ref) => null);

class LocationListUi extends ConsumerWidget {
  const LocationListUi({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final locationAsync = ref.watch(fetchLocationProvider);
    final selectValue = ref.watch(locationValueProvider);

    return locationAsync.when(
      data: (location) {
        // locationが空の場合
        if (location.isEmpty) {
          return Center(
            child: Text(
              '選択可能な項目がありません',
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          );
        }

        // `value`が`items`に一致する場合のみ`DropdownButton`を描画
        // 重複の排除：一意な`kaijyo_code`を持つアイテムを選択
        final uniqueLocations = location.toSet().toList(); // 重複を排除
        debugPrint(uniqueLocations.toString());
        // `value`が`items`に存在することを確認
        if (selectValue == null ||
            !uniqueLocations.any((loc) => loc.kaijyo_code == selectValue)) {
          // 初期値を設定
          WidgetsBinding.instance.addPostFrameCallback((_) {
            // `value`が`items`内に一致しない場合、リストの最初のアイテムを`value`に設定
            ref.read(locationValueProvider.notifier).state =
                uniqueLocations.isNotEmpty
                    ? uniqueLocations.first.kaijyo_code
                    : null;
          });

          return const Center(child: CircularProgressIndicator());
        }

        return Column(
          children: [
            // DropdownButtonをラップして、スクロール可能にする
            Expanded(
              child: ListView(
                children: [
                  DropdownButton<String>(
                    value: selectValue,
                    onChanged: (String? newValue) {
                      ref.read(locationValueProvider.notifier).state =
                          newValue!;
                    },
                    items: uniqueLocations.map((loc) {
                      return DropdownMenuItem<String>(
                        value: loc.kaijyo_code,
                        child: Text(loc.kaijyo_name),
                      );
                    }).toList(),
                    isExpanded: true,
                  ),
                ],
              ),
            ),
          ],
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (e, _) => Center(
        child: Text(
          'データの取得に失敗しました: $e',
          style: Theme.of(context).textTheme.bodyLarge,
        ),
      ),
    );
  }
}

// final locationValueProvider = StateProvider<String?>((ref) => null);

// class LocationListUi extends ConsumerWidget {
//   const LocationListUi({super.key});

//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final locationAsync = ref.watch(fetchLocationProvider);
//     final selectValue = ref.watch(locationValueProvider);

//     return locationAsync.when(
//       data: (location) {
//         // デバッグ用ログ
//         debugPrint('Fetched locations: $location');
//         debugPrint('Current selectValue: $selectValue');

//         // locationが空の場合
//         if (location.isEmpty) {
//           debugPrint('Location list is empty.');
//           return Center(
//             child: Text(
//               '選択可能な項目がありません',
//               style: Theme.of(context).textTheme.bodyLarge,
//             ),
//           );
//         }

//         // 初期値が設定されていない、または一致していない場合
//         if (selectValue == null ||
//             !location.any((loc) => loc.kaijyo_code == selectValue)) {
//           debugPrint('Setting initial value to: ${location.first.kaijyo_code}');
//           WidgetsBinding.instance.addPostFrameCallback((_) {
//             ref.read(locationValueProvider.notifier).state =
//                 location.first.kaijyo_code;
//           });

//           // 初期化中はローディングを表示
//           return const Center(child: CircularProgressIndicator());
//         }

//         // `value`が`items`に一致する場合のみ`DropdownButton`を描画
//         return DropdownButton<String>(
//           value: selectValue,
//           onChanged: (String? newValue) {
//             debugPrint('DropdownButton value changed to: $newValue');
//             ref.read(locationValueProvider.notifier).state = newValue!;
//           },
//           items: location.map((loc) {
//             debugPrint('Adding DropdownMenuItem: ${loc.kaijyo_code}');
//             return DropdownMenuItem<String>(
//               value: loc.kaijyo_code,
//               child: Text(loc.kaijyo_name),
//             );
//           }).toList(),
//           isExpanded: true,
//         );
//       },
//       loading: () {
//         debugPrint('Loading locations...');
//         return const Center(child: CircularProgressIndicator());
//       },
//       error: (e, _) {
//         debugPrint('Error fetching locations: $e');
//         return Center(
//           child: Text(
//             'データの取得に失敗しました: $e',
//             style: Theme.of(context).textTheme.bodyLarge,
//           ),
//         );
//       },
//     );
//   }
// }
