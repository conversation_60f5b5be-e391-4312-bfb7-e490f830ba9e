import 'package:flutter/material.dart';

class CoreButton extends StatelessWidget {
  const CoreButton({
    super.key,
    required this.onPressed,
    this.bgColor = Colors.green,
    this.fgColor = Colors.white,
    required this.title,
    this.fontSize = 14,
  });

  final VoidCallback onPressed;
  final Color bgColor;
  final Color fgColor;
  final String title;
  final double fontSize;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: bgColor,
        minimumSize: const Size(100, 40),
      ),
      child: Text(
        title,
        style: TextStyle(color: fgColor, fontSize: fontSize),
      ),
    );
  }
}
