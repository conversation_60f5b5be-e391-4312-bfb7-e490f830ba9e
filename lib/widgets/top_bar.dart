import 'package:flutter/material.dart';

class TopBar extends StatelessWidget {
  const TopBar({super.key, required this.title, this.showCloseButton = false});

  final String title;
  final bool? showCloseButton;

  @override
  // Widget build(BuildContext context) {
  //   return Expanded(
  //     child: Container(
  //       alignment: Alignment.center,
  //       height: 60,
  //       color: Theme.of(context).colorScheme.inversePrimary,
  //       child: Center(
  //         child: Text(
  //           title == '' ? 'ボイラ・クレーン安全協会' : title,
  //           style: const TextStyle(
  //             fontWeight: FontWeight.bold,
  //             fontSize: 20,
  //           ),
  //         ),
  //       ),
  //     ),
  //   );
  // }

  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        alignment: Alignment.center,
        height: 60,
        color: Theme.of(context).colorScheme.inversePrimary,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // An empty widget to take up space on the left side
            showCloseButton == true
                ? const SizedBox(width: 48)
                : const SizedBox(width: 0), // Adjust width as needed

            // Title text in the center
            Center(
              child: Text(
                title == '' ? 'ボイラ・クレーン安全協会' : title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
              ),
            ),

            // Close button on the right
            showCloseButton == true
                ? IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () {
                      Navigator.of(context).pop(); // Closes the current screen
                    },
                  )
                : const SizedBox(width: 0),
          ],
        ),
      ),
    );
  }
}
