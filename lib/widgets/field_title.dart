import 'package:flutter/material.dart';

class FieldTitle extends StatelessWidget {
  final String title;
  final Color txtColor;
  final Alignment align;

  const FieldTitle({
    super.key,
    required this.title,
    this.txtColor = Colors.black,
    this.align = Alignment.centerLeft,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(right: 10, left: 10, top: 0, bottom: 0),
      alignment: align,
      height: 40,
      color: Colors.grey.shade200,
      child: Center(
        child: Text(
          title,
          style: TextStyle(
              fontSize: 16, fontWeight: FontWeight.bold, color: txtColor),
        ),
      ),
    );
  }
}
