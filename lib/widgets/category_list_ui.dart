// presentation/location_ui.dart
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:koushuu_system/repositories/category_repository.dart';

final categoryValueProvider = StateProvider<String?>((ref) => null);

// ignore: must_be_immutable
class CategoryListUi extends ConsumerWidget {
  const CategoryListUi(this.name, {super.key});

  final String name;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final fetchAsync = ref.watch(fetchCategoryProvider);
    final selectValue = ref.watch(categoryValueProvider);

    return fetchAsync.when(
      data: (lst) => FormBuilderDropdown<String>(
        name: name,
        initialValue: selectValue,
        validator: (value) {
          if (value == null || value.isEmpty) {
            return '講習カテゴリを選択してください。';
          }
          return null;
        },
        onChanged: (String? newValue) {
          ref.read(categoryValueProvider.notifier).state = newValue!;
        },
        items: lst.map((loc) {
          return DropdownMenuItem<String>(
            value: loc.kosyu_category_code,
            child: Text(loc.kosyu_category_name),
          );
        }).toList(),
        isExpanded: true,
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (e, _) => const Center(child: Text("講習カテゴリ見つかりませんでした。")),
    );
  }
}
