import 'package:flutter/material.dart';

class CourseInfoCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final String description;

  const CourseInfoCard({
    super.key,
    required this.title,
    required this.subtitle,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(8.0),
      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Title section with blue background
          Container(
            color: Colors.green, // You can customize the color if needed
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Center(
              child: Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(height: 8.0),

          // Subtitle section
          Text(
            subtitle,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8.0),
          Container(
            color: Colors.grey, // You can customize the color if needed
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: const Center(
              child: Text(
                "現在保有している資格及び業務経験",
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

          const SizedBox(height: 8.0),

          // Description section
          Text(
            description,
            style: const TextStyle(
              fontWeight: FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }
}
