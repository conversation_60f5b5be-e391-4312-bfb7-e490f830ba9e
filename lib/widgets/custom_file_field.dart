import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:file_picker/file_picker.dart'; // PlatformFile / FilePicker
import 'package:image_picker/image_picker.dart';
import 'package:mime/mime.dart';
import 'dart:html' as html;

/// 使い方：
/// FormBuilder(
///   key: _formKey,
///   child: CustomFileField(
///     name: 'shikaku3',
///     label: '資格 3',
///   ),
/// )
///
/// 送信時に Base64 を取りたい場合:
/// final files = _formKey.currentState!.value['shikaku3'] as List<PlatformFile>? ?? [];
/// final payload = files.map((f) => CustomFileField.toBase64Record(f)).toList();

// final base64Str = '...'; // 既存データ
// final pf = CustomFileField.platformFileFromBase64(base64Str, name: 'document.pdf');

// FormBuilder(
//   key: _formKey,
//   initialValue: {
//     'shikaku3': [pf], // ← これで最初からPDFアイコン or 画像プレビューが出ます
//   },
//   child: CustomFileField(name: 'shikaku3', label: '資格 3'),
// );

class CustomFileField extends StatelessWidget {
  final String name;
  final String label;
  final double height;
  final List<String> allowedExtensions; // 画像 + PDF
  final bool allowCamera;
  final bool viewMode;
  final List<PlatformFile>? initValues;

  const CustomFileField(
    String fieldName, {
    super.key,
    required this.name,
    required this.label,
    this.height = 150,
    this.allowedExtensions = const ['jpg', 'jpeg', 'png', 'pdf'],
    this.allowCamera = true,
    this.viewMode = false,
    this.initValues,
  });

  // -------- Base64 <-> PlatformFile 支援 --------

  /// PlatformFile -> {name, mime, base64} へ変換
  static Map<String, String> toBase64Record(PlatformFile f) {
    final bytes = f.bytes;
    // FilePicker で withData: true を使っている前提。なければ空配列に。
    final b = bytes ?? const <int>[];
    final mime =
        lookupMimeType(f.name, headerBytes: b) ?? 'application/octet-stream';
    return {
      'name': f.name,
      'mime': mime,
      'base64': base64Encode(b),
    };
  }

  /// Base64 から PlatformFile を生成（拡張子は MIME から推定、なければ .bin）
  static PlatformFile platformFileFromBase64(String base64Data,
      {String? name}) {
    final bytes = base64Decode(base64Data);
    final mime =
        lookupMimeType('', headerBytes: bytes) ?? 'application/octet-stream';
    final ext = _mimeToExt(mime) ?? 'bin';
    final finalName = name == null
        ? 'file_from_base64.$ext'
        : (name.contains('.') ? name : '$name.$ext');

    return PlatformFile(
      name: finalName,
      size: bytes.length,
      bytes: bytes,
      // path は Web では使わないので null のままでOK
    );
  }

  static String? _mimeToExt(String mime) {
    switch (mime) {
      case 'application/pdf':
        return 'pdf';
      case 'image/png':
        return 'png';
      case 'image/jpeg':
        return 'jpg';
      case 'image/gif':
        return 'gif';
      case 'image/webp':
        return 'webp';
      default:
        return null;
    }
  }

  static String _extFromName(String name) {
    final i = name.lastIndexOf('.');
    return (i >= 0 && i < name.length - 1)
        ? name.substring(i + 1).toLowerCase()
        : '';
  }

  static bool _isPdf(PlatformFile f) {
    final ext = _extFromName(f.name);
    if (ext == 'pdf') return true;
    final mime =
        (f.bytes != null) ? lookupMimeType('', headerBytes: f.bytes) : null;
    return mime == 'application/pdf';
  }

  static bool _isImage(PlatformFile f) {
    final ext = _extFromName(f.name);
    if (['jpg', 'jpeg', 'png', 'gif', 'webp'].contains(ext)) return true;
    final mime =
        (f.bytes != null) ? lookupMimeType('', headerBytes: f.bytes) : null;
    return mime != null && mime.startsWith('image/');
  }

  Future<void> _pickFileWeb(
    FormFieldState<List<PlatformFile>> field,
    List<String> allowedExtensions,
  ) async {
    final uploadInput = html.FileUploadInputElement();
    uploadInput.accept = allowedExtensions.map((ext) {
      if (ext == 'pdf') return 'application/pdf';
      if (['jpg', 'jpeg', 'png', 'gif', 'webp'].contains(ext)) return 'image/*';
      return '.$ext';
    }).join(",");

    uploadInput.click();

    await uploadInput.onChange.first;
    final file = uploadInput.files?.first;
    if (file != null) {
      final reader = html.FileReader();
      reader.readAsArrayBuffer(file);
      await reader.onLoad.first;

      final bytes = reader.result as Uint8List;
      final pf = PlatformFile(
        name: file.name,
        size: file.size,
        bytes: bytes,
      );
      field.didChange([pf]);
    }
  }

  // Future<void> _pickFileWeb(
  //   FormFieldState<List<PlatformFile>> field,
  //   List<String> allowedExtensions,
  // ) async {
  //   final uploadInput = html.FileUploadInputElement();

  //   // 画像・PDFのみ許可
  //   uploadInput.accept = allowedExtensions.map((ext) {
  //     if (ext == 'pdf') return 'application/pdf';
  //     if (['jpg', 'jpeg', 'png', 'gif', 'webp'].contains(ext)) return 'image/*';
  //     return '.$ext';
  //   }).join(',');

  //   // iOS Safari の場合、必ず capture 属性を付けるとカメラも選べる
  //   // uploadInput.capture = 'environment'; // ← カメラ起動したい場合

  //   uploadInput.click(); // ✅ ユーザー操作中に即呼ぶ

  //   uploadInput.onChange.listen((_) async {
  //     final file = uploadInput.files?.first;
  //     if (file == null) return;

  //     final reader = html.FileReader();
  //     reader.readAsArrayBuffer(file);
  //     await reader.onLoad.first;

  //     final bytes = reader.result as Uint8List;
  //     final pf = PlatformFile(
  //       name: file.name,
  //       size: file.size,
  //       bytes: bytes,
  //     );
  //     field.didChange([pf]);
  //   });
  // }

  Future<void> _showSourceSheet(
    BuildContext context,
    FormFieldState<List<PlatformFile>> field,
  ) async {
    final picker = ImagePicker();

    await showModalBottomSheet(
      context: context,
      builder: (_) => SafeArea(
        child: Wrap(
          children: [
            if (allowCamera)
              ListTile(
                leading: const Icon(Icons.camera_alt, color: Colors.orange),
                title: const Text('カメラで撮影'),
                onTap: () async {
                  try {
                    final shot = await picker.pickImage(
                        source: ImageSource.camera, imageQuality: 85);
                    if (shot != null) {
                      final bytes = await shot.readAsBytes();
                      final pf = PlatformFile(
                        name: shot.name,
                        size: bytes.length,
                        bytes: bytes,
                        // Web は path を使わない
                      );
                      field.didChange([pf]);
                    }
                  } finally {
                    Navigator.of(context).pop();
                  }
                },
              ),
            ListTile(
              leading: const Icon(Icons.insert_drive_file, color: Colors.blue),
              title: allowedExtensions.contains("pdf")
                  ? const Text('ファイルから選択（画像 / PDF）')
                  : const Text('ファイルから選択'),
              onTap: () {
                Navigator.of(context).pop();
                if (kIsWeb) {
                  // ✅ モバイルWeb対応版
                  _pickFileWeb(field, allowedExtensions);
                } else {
                  FilePicker.platform
                      .pickFiles(
                    allowMultiple: false,
                    type: FileType.custom,
                    allowedExtensions: allowedExtensions,
                    withData: true, // ← これで PlatformFile.bytes が入る(Web/モバイル両対応)
                  )
                      .then((result) {
                    if (result != null && result.files.isNotEmpty) {
                      field.didChange([result.files.first]);
                    }
                  });
                  // if (result != null && result.files.isNotEmpty) {
                  //   field.didChange([result.files.first]);
                  // }
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return FormBuilderField<List<PlatformFile>>(
      name: name,
      initialValue: initValues ?? [],
      builder: (field) {
        final files = field.value ?? const <PlatformFile>[];
        final selected = files.isNotEmpty ? files.first : null;

        // 外枠 + ラベル
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 6.0, left: 4.0),
              child: Text(label, style: const TextStyle(fontSize: 14)),
            ),
            InkWell(
              onTap: () => viewMode ? null : _showSourceSheet(context, field),
              // ✅ モバイルWeb対応版
              // _pickFileWeb(field,
              //     allowedExtensions), // _showSourceSheet(context, field),
              child: Container(
                height: height,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey[600],
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: Colors.grey[400]!),
                ),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // 中央表示（未選択: カメラアイコン / 画像: プレビュー / PDF: PDFアイコン）
                    if (selected == null)
                      const Icon(Icons.camera_alt,
                          size: 40, color: Colors.white70)
                    else
                      _buildPreview(selected),

                    // 右上クリアボタン
                    if (!viewMode)
                      if (selected != null)
                        Positioned(
                          right: 8,
                          top: 8,
                          child: GestureDetector(
                            onTap: () => field.didChange([]),
                            child: Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.black.withOpacity(0.5),
                              ),
                              padding: const EdgeInsets.all(4),
                              child: const Icon(Icons.close,
                                  size: 16, color: Colors.white),
                            ),
                          ),
                        ),
                  ],
                ),
              ),
            ),
            if (field.errorText != null) ...[
              const SizedBox(height: 6),
              Text(field.errorText!,
                  style: TextStyle(color: Theme.of(context).colorScheme.error)),
            ],
          ],
        );
      },
    );
  }

  Widget _buildPreview(PlatformFile f) {
    if (_isPdf(f)) {
      // PDF はアイコンを中央
      return const Icon(Icons.picture_as_pdf,
          size: 60, color: Colors.redAccent);
    }
    if (_isImage(f)) {
      if (f.bytes != null) {
        return Image.memory(
          f.bytes!,
          fit: BoxFit.contain, // 枠内に収める（崩れ防止）
          width: double.infinity,
          height: double.infinity,
        );
      }
      // bytes が無いケースはまれだが、念のためファイルアイコン
      return const Icon(Icons.broken_image, size: 40, color: Colors.white70);
    }
    // その他
    return const Icon(Icons.insert_drive_file, size: 40, color: Colors.white70);
  }
}
