import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:koushuu_system/models/participant.dart';
import 'package:koushuu_system/screens/change.dart';
import 'package:koushuu_system/screens/change_desc.dart';
import 'package:koushuu_system/screens/change_email_sent.dart';
import 'package:koushuu_system/screens/change_end.dart';
import 'package:koushuu_system/screens/change_request.dart';
import 'package:koushuu_system/screens/login.dart';
import 'package:koushuu_system/screens/mep.dart';
import 'package:koushuu_system/screens/participants.dart';
import 'package:koushuu_system/screens/password_reset.dart';
import 'package:koushuu_system/screens/reg_participant.dart';
import 'package:koushuu_system/screens/regend_participant.dart';
import 'package:koushuu_system/screens/register.dart';
import 'package:koushuu_system/screens/result.dart';
import 'package:koushuu_system/screens/result_sent.dart';
import 'package:koushuu_system/screens/sample.dart';
import 'package:koushuu_system/screens/search.dart';
import 'package:koushuu_system/screens/home.dart';

/// The route configuration.
final GoRouter router = GoRouter(
  routes: <RouteBase>[
    GoRoute(
      path: '/',
      builder: (BuildContext context, GoRouterState state) {
        return const HomePage(title: 'ボイラ・クレーン安全協会');
      },
      routes: <RouteBase>[
        GoRoute(
          path: 'search',
          builder: (BuildContext context, GoRouterState state) {
            return const SearchPage(
              title: "講習検索",
            );
          },
        ),
        GoRoute(
          path: 'sample',
          builder: (BuildContext context, GoRouterState state) {
            return const SamplePage(
              title: "サンプル",
            );
          },
        ),
        GoRoute(
          path: 'result',
          builder: (BuildContext context, GoRouterState state) {
            if (state.extra != null) {
              final Map<String, dynamic> objParam =
                  state.extra as Map<String, dynamic>;
              final String jimusho = objParam['jimusho'] ?? "";
              final String category = objParam['category'] ?? "";
              final String keyword = objParam['keyword'] ?? "";
              final String startDate =
                  objParam['startDate'] ?? "1900-01-01 00:00:00";
              final String endDate =
                  objParam['endDate'] ?? "1900-01-01 00:00:00";

              return SearchResultPage(
                title: "検索結果",
                jimusho: jimusho,
                category: category,
                keyword: keyword,
                startDate: DateTime.parse(startDate),
                endDate: DateTime.parse(endDate),
              );
            }
            return const HomePage(title: "ボイラ・クレーン安全協会'");
          },
        ),
        GoRoute(
          path: 'resultsent',
          builder: (BuildContext context, GoRouterState state) {
            return const SearchResultSentPage();
          },
        ),
        GoRoute(
          path: 'login',
          builder: (BuildContext context, GoRouterState state) {
            return LoginPage(
              title: "ログイン",
              // company: "",
              // memname: "",
              // memid: "",
            );
          },
        ),
        GoRoute(
          path: 'respass',
          builder: (BuildContext context, GoRouterState state) {
            return PasswordResetPage(
              title: "パスワードリセット手続き",
            );
          },
        ),
        GoRoute(
          path: 'register',
          builder: (BuildContext context, GoRouterState state) {
            // String mid = state.uri.queryParameters["mid"] as String;
            // String mname = state.uri.queryParameters["mname"] as String;
            // String email = state.uri.queryParameters["email"] as String;
            final Map<String, dynamic> objExtra =
                state.extra as Map<String, dynamic>;
            String mid = objExtra["mid"] as String;
            String mname = objExtra["mname"] as String;
            String email = objExtra["email"] as String;
            String memname = objExtra["memname"] ?? "";
            String coursename = objExtra["coursename"] ?? "";
            String companyname = objExtra["companyname"] ?? "";

            return RegisterPage(
              title: "申込者登録　パスワード設定",
              email: email,
              company: mname,
              id: mid,
              memname: memname,
              coursename: coursename,
              companyname: companyname,
            );
          },
        ),
        GoRoute(
          path: 'change',
          builder: (BuildContext context, GoRouterState state) {
            return const ChangePage(
              title: "再交付の案内",
            );
          },
        ),
        GoRoute(
          path: 'changedesc',
          builder: (BuildContext context, GoRouterState state) {
            return const ChangeDescPage(
              title: "統合修了証の説明",
            );
          },
        ),
        GoRoute(
          path: 'changereq',
          builder: (BuildContext context, GoRouterState state) {
            return ChangeRequestPage(
              title: "再発行依頼",
            );
          },
        ),
        GoRoute(
          path: 'changeend',
          builder: (BuildContext context, GoRouterState state) {
            return const ChangeEmailSentPage(
              title: "",
            );
          },
        ),
        GoRoute(
          path: 'changereqend',
          builder: (BuildContext context, GoRouterState state) {
            String uketsukeid = "0";
            state.uri.queryParameters.forEach((key, value) {
              if (key == "uid") {
                uketsukeid = value;
              }
            });

            // if (state.extra != null) {
            //   final Map<String, dynamic> objParam =
            //       state.extra as Map<String, dynamic>;
            //   uketsukeid = objParam['uid'] ?? "0";
            // }
            return ChangeEndPage(
              title: "",
              uketsukeid: uketsukeid,
            );
          },
        ),

        GoRoute(
          path: 'participants',
          builder: (BuildContext context, GoRouterState state) {
            // final Map<String, dynamic> objExtra =
            // state.extra as Map<String, dynamic>;
            // String appId = objExtra["appId"] as String;

            return ParticipantsPage(
              title: "受講者一覧",
              // applicationId: appId,
            );
          },
        ),
        GoRoute(
          path: 'rparticipant',
          builder: (BuildContext context, GoRouterState state) {
            return RegParticipantPage(
              title: "受講者情報登録",
              p: state.extra == null ? null : state.extra as Participant,
            );
          },
        ),
        GoRoute(
          path: 'eparticipant',
          builder: (BuildContext context, GoRouterState state) {
            int sParticipantsButton = 0;
            if (state.extra != null) {
              final Map<String, dynamic> objExtra =
                  state.extra as Map<String, dynamic>;
              sParticipantsButton = objExtra["showParticipantsButton"] as int;
            }
            return RegEndParticipantPage(
              title: "受講者情報登録完了",
              showParticipantsButton: sParticipantsButton,
            );
          },
        ),
        GoRoute(
          path: 'mep',
          builder: (BuildContext context, GoRouterState state) {
            String ek = "";
            String uid = "";
            if (state.uri.queryParameters.isNotEmpty) {
              if (state.uri.queryParameters["ek"] != null) {
                ek = state.uri.queryParameters["ek"].toString();
              }
              if (state.uri.queryParameters["uid"] != null) {
                uid = state.uri.queryParameters["uid"].toString();
              }
            }

            return MailEndPointPage(
              title: "",
              ek: ek,
              uid: uid,
            );
          },
        ),

        // GoRoute(
        //   path: 'reservation',
        //   builder: (BuildContext context, GoRouterState state) {
        //     final Object titlePage = state.extra ?? '';
        //     return ReservationPage(
        //       title: titlePage as String,
        //     );
        //   },
        // ),
      ],
    ),
  ],
);
