import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:koushuu_system/core/api_service.dart';
import 'package:koushuu_system/core/env_config.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:koushuu_system/core/constants.dart' as constants;

part 'participant_repository.g.dart';

class ParticipantRepository {
  Future<Map<String, dynamic>> getParticipants(
      String applicationId, String email) async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/participants";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
      //'authorization': 'Bearer ${dotenv.get("ACCESSTOKEN")}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);

    final res = await apiService.postRequest(
      strPath,
      data: {
        "application_id": applicationId,
        "email": email,
      },
    );

    String resReplaced = res.toString();
    resReplaced = resReplaced.replaceAll(constants.DB_NULL_VALUE, "");
    resReplaced = resReplaced.replaceAll(constants.DB_NULL_DATETIME, "");
    resReplaced = resReplaced.replaceAll(constants.DB_NULL_DATE, "");

    final Map<String, dynamic> jsonData = json.decode(resReplaced);
    return jsonData;
  }

  Future<Map<String, dynamic>> newParticipant(Map<String, dynamic> p) async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/rparticipant";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
      //'authorization': 'Bearer ${dotenv.get("ACCESSTOKEN")}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);
    final res = await apiService.postRequest(
      strPath,
      data: p,
      // {
      //   "application_id": applicationId,
      //   ...p,
      // },
    );

    final Map<String, dynamic> jsonData = json.decode(res.toString());
    return jsonData;
  }

  Future<Map<String, dynamic>> setParticipant(Map<String, dynamic> p) async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/sparticipant";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
      //'authorization': 'Bearer ${dotenv.get("ACCESSTOKEN")}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);
    final res = await apiService.postRequest(
      strPath,
      data: p,
    );

    final Map<String, dynamic> jsonData = json.decode(res.toString());
    return jsonData;
  }

  Future<Map<String, dynamic>> getParticipant(
      String? participantId, String? uketsukeId) async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/participant";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
      //'authorization': 'Bearer ${dotenv.get("ACCESSTOKEN")}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);

    final res = await apiService.postRequest(
      strPath,
      data: {
        "participant_id": participantId,
        "uketsuke_id": uketsukeId,
      },
    );

    String resReplaced = res.toString();
    resReplaced = resReplaced.replaceAll(constants.DB_NULL_VALUE, "");
    resReplaced = resReplaced.replaceAll(constants.DB_NULL_DATETIME, "");
    resReplaced = resReplaced.replaceAll(constants.DB_NULL_DATE, "");

    final Map<String, dynamic> jsonData = json.decode(resReplaced);
    return jsonData;
  }
}

// this will generate a ParticipantRepositoryProvider
@riverpod
ParticipantRepository participantRepository(Ref ref) {
  return ParticipantRepository();
}

@riverpod
Future<dynamic> getParticipants(Ref ref, String applicationId, String email) {
  return ref
      .watch(participantRepositoryProvider)
      .getParticipants(applicationId, email);
}

@riverpod
Future<dynamic> getParticipant(
    Ref ref, String? participantId, String? uketsukeId) {
  return ref
      .watch(participantRepositoryProvider)
      .getParticipant(participantId, uketsukeId);
}

@riverpod
Future<dynamic> makeParticipant(Ref ref, Map<String, dynamic> p) {
  return ref.watch(participantRepositoryProvider).newParticipant(p);
}

@riverpod
Future<dynamic> setParticipant(Ref ref, Map<String, dynamic> p) {
  return ref.watch(participantRepositoryProvider).setParticipant(p);
}
