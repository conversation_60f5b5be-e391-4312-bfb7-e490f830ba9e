import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:koushuu_system/core/api_service.dart';
import 'package:koushuu_system/core/env_config.dart';
import 'package:koushuu_system/extensions/extensions.dart';
import 'package:koushuu_system/models/category.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'category_repository.g.dart';

class CategoryRepository {
  Future<List<Category>> fetchData() async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/getcategorylist";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);

    Response postResponse = await apiService.postRequest(
      strPath,
      data: {},
      // queryParameters: {
      //   'param1': 'value1',
      // },
    );

    //debugPrint(postResponse.data);
    //debugPrint(postResponse.data.responsedata);
    var jsonData = json.decode(postResponse.toString());

    // debugPrint("-------dddddd-------");
    // debugPrint(jsonData.toString());
    // debugPrint("-------eeeeee-------");
    // debugPrint(jsonData["responsedata"]["kaijyo_list"].toString());
    // debugPrint("-------cccccc-------");

    Iterable l = jsonData["responsedata"]["kosyu_category_list"];
    List<Category> lst =
        List<Category>.from(l.map((model) => Category.fromJson(model)));

    final result = lst
        .whereWithIndex((element, index) =>
            lst.indexWhere((element2) =>
                element2.kosyu_category_code == element.kosyu_category_code) ==
            index)
        .toList();

    // for (var element in result) {
    //   print(element.toJson());
    // }

    return result;
  }
}

// this will generate a CategoryRepositoryProvider
@riverpod
CategoryRepository categoryRepository(Ref ref) {
  return CategoryRepository();
}

@riverpod
Future<List<Category>> fetchCategory(Ref ref) {
  return ref.watch(categoryRepositoryProvider).fetchData();
}
