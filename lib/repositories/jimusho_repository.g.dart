// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'jimusho_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$jimushoRepositoryHash() => r'bd3a0993cbef0927eb9b04a8e414e1962bdee2dc';

/// See also [jimushoRepository].
@ProviderFor(jimushoRepository)
final jimushoRepositoryProvider =
    AutoDisposeProvider<JimushoRepository>.internal(
  jimushoRepository,
  name: r'jimushoRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$jimushoRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef JimushoRepositoryRef = AutoDisposeProviderRef<JimushoRepository>;
String _$fetchJimushoListHash() => r'ed382ab499bc26d410098c779c45a81b9aef359b';

/// See also [fetchJimushoList].
@ProviderFor(fetchJimushoList)
final fetchJimushoListProvider =
    AutoDisposeFutureProvider<List<Map<String, dynamic>>>.internal(
  fetchJimushoList,
  name: r'fetchJimushoListProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchJimushoListHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchJimushoListRef
    = AutoDisposeFutureProviderRef<List<Map<String, dynamic>>>;
String _$fetchKoushuuKubunListHash() =>
    r'c4626d6a5883b6ce2e3dd902bdb7848ebef65433';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchKoushuuKubunList].
@ProviderFor(fetchKoushuuKubunList)
const fetchKoushuuKubunListProvider = FetchKoushuuKubunListFamily();

/// See also [fetchKoushuuKubunList].
class FetchKoushuuKubunListFamily
    extends Family<AsyncValue<List<Map<String, dynamic>>>> {
  /// See also [fetchKoushuuKubunList].
  const FetchKoushuuKubunListFamily();

  /// See also [fetchKoushuuKubunList].
  FetchKoushuuKubunListProvider call(
    String jimusyo_code,
  ) {
    return FetchKoushuuKubunListProvider(
      jimusyo_code,
    );
  }

  @override
  FetchKoushuuKubunListProvider getProviderOverride(
    covariant FetchKoushuuKubunListProvider provider,
  ) {
    return call(
      provider.jimusyo_code,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchKoushuuKubunListProvider';
}

/// See also [fetchKoushuuKubunList].
class FetchKoushuuKubunListProvider
    extends AutoDisposeFutureProvider<List<Map<String, dynamic>>> {
  /// See also [fetchKoushuuKubunList].
  FetchKoushuuKubunListProvider(
    String jimusyo_code,
  ) : this._internal(
          (ref) => fetchKoushuuKubunList(
            ref as FetchKoushuuKubunListRef,
            jimusyo_code,
          ),
          from: fetchKoushuuKubunListProvider,
          name: r'fetchKoushuuKubunListProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchKoushuuKubunListHash,
          dependencies: FetchKoushuuKubunListFamily._dependencies,
          allTransitiveDependencies:
              FetchKoushuuKubunListFamily._allTransitiveDependencies,
          jimusyo_code: jimusyo_code,
        );

  FetchKoushuuKubunListProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.jimusyo_code,
  }) : super.internal();

  final String jimusyo_code;

  @override
  Override overrideWith(
    FutureOr<List<Map<String, dynamic>>> Function(
            FetchKoushuuKubunListRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchKoushuuKubunListProvider._internal(
        (ref) => create(ref as FetchKoushuuKubunListRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        jimusyo_code: jimusyo_code,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Map<String, dynamic>>> createElement() {
    return _FetchKoushuuKubunListProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchKoushuuKubunListProvider &&
        other.jimusyo_code == jimusyo_code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, jimusyo_code.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchKoushuuKubunListRef
    on AutoDisposeFutureProviderRef<List<Map<String, dynamic>>> {
  /// The parameter `jimusyo_code` of this provider.
  String get jimusyo_code;
}

class _FetchKoushuuKubunListProviderElement
    extends AutoDisposeFutureProviderElement<List<Map<String, dynamic>>>
    with FetchKoushuuKubunListRef {
  _FetchKoushuuKubunListProviderElement(super.provider);

  @override
  String get jimusyo_code =>
      (origin as FetchKoushuuKubunListProvider).jimusyo_code;
}

String _$fetchKoushuuListHash() => r'eabaa171731d88103a51bcae4d40fe628f2af42a';

/// See also [fetchKoushuuList].
@ProviderFor(fetchKoushuuList)
const fetchKoushuuListProvider = FetchKoushuuListFamily();

/// See also [fetchKoushuuList].
class FetchKoushuuListFamily
    extends Family<AsyncValue<List<Map<String, dynamic>>>> {
  /// See also [fetchKoushuuList].
  const FetchKoushuuListFamily();

  /// See also [fetchKoushuuList].
  FetchKoushuuListProvider call(
    String jimusyo_code,
    String kosyu_kubun,
  ) {
    return FetchKoushuuListProvider(
      jimusyo_code,
      kosyu_kubun,
    );
  }

  @override
  FetchKoushuuListProvider getProviderOverride(
    covariant FetchKoushuuListProvider provider,
  ) {
    return call(
      provider.jimusyo_code,
      provider.kosyu_kubun,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchKoushuuListProvider';
}

/// See also [fetchKoushuuList].
class FetchKoushuuListProvider
    extends AutoDisposeFutureProvider<List<Map<String, dynamic>>> {
  /// See also [fetchKoushuuList].
  FetchKoushuuListProvider(
    String jimusyo_code,
    String kosyu_kubun,
  ) : this._internal(
          (ref) => fetchKoushuuList(
            ref as FetchKoushuuListRef,
            jimusyo_code,
            kosyu_kubun,
          ),
          from: fetchKoushuuListProvider,
          name: r'fetchKoushuuListProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchKoushuuListHash,
          dependencies: FetchKoushuuListFamily._dependencies,
          allTransitiveDependencies:
              FetchKoushuuListFamily._allTransitiveDependencies,
          jimusyo_code: jimusyo_code,
          kosyu_kubun: kosyu_kubun,
        );

  FetchKoushuuListProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.jimusyo_code,
    required this.kosyu_kubun,
  }) : super.internal();

  final String jimusyo_code;
  final String kosyu_kubun;

  @override
  Override overrideWith(
    FutureOr<List<Map<String, dynamic>>> Function(FetchKoushuuListRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchKoushuuListProvider._internal(
        (ref) => create(ref as FetchKoushuuListRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        jimusyo_code: jimusyo_code,
        kosyu_kubun: kosyu_kubun,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Map<String, dynamic>>> createElement() {
    return _FetchKoushuuListProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchKoushuuListProvider &&
        other.jimusyo_code == jimusyo_code &&
        other.kosyu_kubun == kosyu_kubun;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, jimusyo_code.hashCode);
    hash = _SystemHash.combine(hash, kosyu_kubun.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchKoushuuListRef
    on AutoDisposeFutureProviderRef<List<Map<String, dynamic>>> {
  /// The parameter `jimusyo_code` of this provider.
  String get jimusyo_code;

  /// The parameter `kosyu_kubun` of this provider.
  String get kosyu_kubun;
}

class _FetchKoushuuListProviderElement
    extends AutoDisposeFutureProviderElement<List<Map<String, dynamic>>>
    with FetchKoushuuListRef {
  _FetchKoushuuListProviderElement(super.provider);

  @override
  String get jimusyo_code => (origin as FetchKoushuuListProvider).jimusyo_code;
  @override
  String get kosyu_kubun => (origin as FetchKoushuuListProvider).kosyu_kubun;
}

String _$fetchKoushuuDateListHash() =>
    r'58bca7d129786cd6550b4845fa0118e201006ac9';

/// See also [fetchKoushuuDateList].
@ProviderFor(fetchKoushuuDateList)
const fetchKoushuuDateListProvider = FetchKoushuuDateListFamily();

/// See also [fetchKoushuuDateList].
class FetchKoushuuDateListFamily
    extends Family<AsyncValue<List<Map<String, dynamic>>>> {
  /// See also [fetchKoushuuDateList].
  const FetchKoushuuDateListFamily();

  /// See also [fetchKoushuuDateList].
  FetchKoushuuDateListProvider call(
    String jimusyo_code,
    String kosyu_kubun,
    String kosyu_code,
  ) {
    return FetchKoushuuDateListProvider(
      jimusyo_code,
      kosyu_kubun,
      kosyu_code,
    );
  }

  @override
  FetchKoushuuDateListProvider getProviderOverride(
    covariant FetchKoushuuDateListProvider provider,
  ) {
    return call(
      provider.jimusyo_code,
      provider.kosyu_kubun,
      provider.kosyu_code,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchKoushuuDateListProvider';
}

/// See also [fetchKoushuuDateList].
class FetchKoushuuDateListProvider
    extends AutoDisposeFutureProvider<List<Map<String, dynamic>>> {
  /// See also [fetchKoushuuDateList].
  FetchKoushuuDateListProvider(
    String jimusyo_code,
    String kosyu_kubun,
    String kosyu_code,
  ) : this._internal(
          (ref) => fetchKoushuuDateList(
            ref as FetchKoushuuDateListRef,
            jimusyo_code,
            kosyu_kubun,
            kosyu_code,
          ),
          from: fetchKoushuuDateListProvider,
          name: r'fetchKoushuuDateListProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchKoushuuDateListHash,
          dependencies: FetchKoushuuDateListFamily._dependencies,
          allTransitiveDependencies:
              FetchKoushuuDateListFamily._allTransitiveDependencies,
          jimusyo_code: jimusyo_code,
          kosyu_kubun: kosyu_kubun,
          kosyu_code: kosyu_code,
        );

  FetchKoushuuDateListProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.jimusyo_code,
    required this.kosyu_kubun,
    required this.kosyu_code,
  }) : super.internal();

  final String jimusyo_code;
  final String kosyu_kubun;
  final String kosyu_code;

  @override
  Override overrideWith(
    FutureOr<List<Map<String, dynamic>>> Function(
            FetchKoushuuDateListRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchKoushuuDateListProvider._internal(
        (ref) => create(ref as FetchKoushuuDateListRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        jimusyo_code: jimusyo_code,
        kosyu_kubun: kosyu_kubun,
        kosyu_code: kosyu_code,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Map<String, dynamic>>> createElement() {
    return _FetchKoushuuDateListProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchKoushuuDateListProvider &&
        other.jimusyo_code == jimusyo_code &&
        other.kosyu_kubun == kosyu_kubun &&
        other.kosyu_code == kosyu_code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, jimusyo_code.hashCode);
    hash = _SystemHash.combine(hash, kosyu_kubun.hashCode);
    hash = _SystemHash.combine(hash, kosyu_code.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchKoushuuDateListRef
    on AutoDisposeFutureProviderRef<List<Map<String, dynamic>>> {
  /// The parameter `jimusyo_code` of this provider.
  String get jimusyo_code;

  /// The parameter `kosyu_kubun` of this provider.
  String get kosyu_kubun;

  /// The parameter `kosyu_code` of this provider.
  String get kosyu_code;
}

class _FetchKoushuuDateListProviderElement
    extends AutoDisposeFutureProviderElement<List<Map<String, dynamic>>>
    with FetchKoushuuDateListRef {
  _FetchKoushuuDateListProviderElement(super.provider);

  @override
  String get jimusyo_code =>
      (origin as FetchKoushuuDateListProvider).jimusyo_code;
  @override
  String get kosyu_kubun =>
      (origin as FetchKoushuuDateListProvider).kosyu_kubun;
  @override
  String get kosyu_code => (origin as FetchKoushuuDateListProvider).kosyu_code;
}

String _$fetchKoushuuKuwariListHash() =>
    r'2b7d1ed05bcc65590e16580e236bae2911fceb10';

/// See also [fetchKoushuuKuwariList].
@ProviderFor(fetchKoushuuKuwariList)
const fetchKoushuuKuwariListProvider = FetchKoushuuKuwariListFamily();

/// See also [fetchKoushuuKuwariList].
class FetchKoushuuKuwariListFamily
    extends Family<AsyncValue<List<Map<String, dynamic>>>> {
  /// See also [fetchKoushuuKuwariList].
  const FetchKoushuuKuwariListFamily();

  /// See also [fetchKoushuuKuwariList].
  FetchKoushuuKuwariListProvider call(
    String jimusyo_code,
    String kosyu_kubun,
    String kosyu_code,
    String kosyu_date,
  ) {
    return FetchKoushuuKuwariListProvider(
      jimusyo_code,
      kosyu_kubun,
      kosyu_code,
      kosyu_date,
    );
  }

  @override
  FetchKoushuuKuwariListProvider getProviderOverride(
    covariant FetchKoushuuKuwariListProvider provider,
  ) {
    return call(
      provider.jimusyo_code,
      provider.kosyu_kubun,
      provider.kosyu_code,
      provider.kosyu_date,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchKoushuuKuwariListProvider';
}

/// See also [fetchKoushuuKuwariList].
class FetchKoushuuKuwariListProvider
    extends AutoDisposeFutureProvider<List<Map<String, dynamic>>> {
  /// See also [fetchKoushuuKuwariList].
  FetchKoushuuKuwariListProvider(
    String jimusyo_code,
    String kosyu_kubun,
    String kosyu_code,
    String kosyu_date,
  ) : this._internal(
          (ref) => fetchKoushuuKuwariList(
            ref as FetchKoushuuKuwariListRef,
            jimusyo_code,
            kosyu_kubun,
            kosyu_code,
            kosyu_date,
          ),
          from: fetchKoushuuKuwariListProvider,
          name: r'fetchKoushuuKuwariListProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchKoushuuKuwariListHash,
          dependencies: FetchKoushuuKuwariListFamily._dependencies,
          allTransitiveDependencies:
              FetchKoushuuKuwariListFamily._allTransitiveDependencies,
          jimusyo_code: jimusyo_code,
          kosyu_kubun: kosyu_kubun,
          kosyu_code: kosyu_code,
          kosyu_date: kosyu_date,
        );

  FetchKoushuuKuwariListProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.jimusyo_code,
    required this.kosyu_kubun,
    required this.kosyu_code,
    required this.kosyu_date,
  }) : super.internal();

  final String jimusyo_code;
  final String kosyu_kubun;
  final String kosyu_code;
  final String kosyu_date;

  @override
  Override overrideWith(
    FutureOr<List<Map<String, dynamic>>> Function(
            FetchKoushuuKuwariListRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchKoushuuKuwariListProvider._internal(
        (ref) => create(ref as FetchKoushuuKuwariListRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        jimusyo_code: jimusyo_code,
        kosyu_kubun: kosyu_kubun,
        kosyu_code: kosyu_code,
        kosyu_date: kosyu_date,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Map<String, dynamic>>> createElement() {
    return _FetchKoushuuKuwariListProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchKoushuuKuwariListProvider &&
        other.jimusyo_code == jimusyo_code &&
        other.kosyu_kubun == kosyu_kubun &&
        other.kosyu_code == kosyu_code &&
        other.kosyu_date == kosyu_date;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, jimusyo_code.hashCode);
    hash = _SystemHash.combine(hash, kosyu_kubun.hashCode);
    hash = _SystemHash.combine(hash, kosyu_code.hashCode);
    hash = _SystemHash.combine(hash, kosyu_date.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchKoushuuKuwariListRef
    on AutoDisposeFutureProviderRef<List<Map<String, dynamic>>> {
  /// The parameter `jimusyo_code` of this provider.
  String get jimusyo_code;

  /// The parameter `kosyu_kubun` of this provider.
  String get kosyu_kubun;

  /// The parameter `kosyu_code` of this provider.
  String get kosyu_code;

  /// The parameter `kosyu_date` of this provider.
  String get kosyu_date;
}

class _FetchKoushuuKuwariListProviderElement
    extends AutoDisposeFutureProviderElement<List<Map<String, dynamic>>>
    with FetchKoushuuKuwariListRef {
  _FetchKoushuuKuwariListProviderElement(super.provider);

  @override
  String get jimusyo_code =>
      (origin as FetchKoushuuKuwariListProvider).jimusyo_code;
  @override
  String get kosyu_kubun =>
      (origin as FetchKoushuuKuwariListProvider).kosyu_kubun;
  @override
  String get kosyu_code =>
      (origin as FetchKoushuuKuwariListProvider).kosyu_code;
  @override
  String get kosyu_date =>
      (origin as FetchKoushuuKuwariListProvider).kosyu_date;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
