// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'course_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$courseRepositoryHash() => r'ef1c5c3fbe611302f4c1f67b9a9ac31aaf3c0f89';

/// See also [courseRepository].
@ProviderFor(courseRepository)
final courseRepositoryProvider = AutoDisposeProvider<CourseRepository>.internal(
  courseRepository,
  name: r'courseRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$courseRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CourseRepositoryRef = AutoDisposeProviderRef<CourseRepository>;
String _$fetchCourseHash() => r'6f87aac9b97536f1b1134ae9584cdee8c2c3e641';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchCourse].
@ProviderFor(fetchCourse)
const fetchCourseProvider = FetchCourseFamily();

/// See also [fetchCourse].
class FetchCourseFamily extends Family<AsyncValue<List<dynamic>>> {
  /// See also [fetchCourse].
  const FetchCourseFamily();

  /// See also [fetchCourse].
  FetchCourseProvider call(
    String jimusyo_code,
    String kosyu_category_code,
    String? freeword,
    String start_date,
    String end_date,
  ) {
    return FetchCourseProvider(
      jimusyo_code,
      kosyu_category_code,
      freeword,
      start_date,
      end_date,
    );
  }

  @override
  FetchCourseProvider getProviderOverride(
    covariant FetchCourseProvider provider,
  ) {
    return call(
      provider.jimusyo_code,
      provider.kosyu_category_code,
      provider.freeword,
      provider.start_date,
      provider.end_date,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchCourseProvider';
}

/// See also [fetchCourse].
class FetchCourseProvider extends AutoDisposeFutureProvider<List<dynamic>> {
  /// See also [fetchCourse].
  FetchCourseProvider(
    String jimusyo_code,
    String kosyu_category_code,
    String? freeword,
    String start_date,
    String end_date,
  ) : this._internal(
          (ref) => fetchCourse(
            ref as FetchCourseRef,
            jimusyo_code,
            kosyu_category_code,
            freeword,
            start_date,
            end_date,
          ),
          from: fetchCourseProvider,
          name: r'fetchCourseProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchCourseHash,
          dependencies: FetchCourseFamily._dependencies,
          allTransitiveDependencies:
              FetchCourseFamily._allTransitiveDependencies,
          jimusyo_code: jimusyo_code,
          kosyu_category_code: kosyu_category_code,
          freeword: freeword,
          start_date: start_date,
          end_date: end_date,
        );

  FetchCourseProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.jimusyo_code,
    required this.kosyu_category_code,
    required this.freeword,
    required this.start_date,
    required this.end_date,
  }) : super.internal();

  final String jimusyo_code;
  final String kosyu_category_code;
  final String? freeword;
  final String start_date;
  final String end_date;

  @override
  Override overrideWith(
    FutureOr<List<dynamic>> Function(FetchCourseRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchCourseProvider._internal(
        (ref) => create(ref as FetchCourseRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        jimusyo_code: jimusyo_code,
        kosyu_category_code: kosyu_category_code,
        freeword: freeword,
        start_date: start_date,
        end_date: end_date,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<dynamic>> createElement() {
    return _FetchCourseProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchCourseProvider &&
        other.jimusyo_code == jimusyo_code &&
        other.kosyu_category_code == kosyu_category_code &&
        other.freeword == freeword &&
        other.start_date == start_date &&
        other.end_date == end_date;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, jimusyo_code.hashCode);
    hash = _SystemHash.combine(hash, kosyu_category_code.hashCode);
    hash = _SystemHash.combine(hash, freeword.hashCode);
    hash = _SystemHash.combine(hash, start_date.hashCode);
    hash = _SystemHash.combine(hash, end_date.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchCourseRef on AutoDisposeFutureProviderRef<List<dynamic>> {
  /// The parameter `jimusyo_code` of this provider.
  String get jimusyo_code;

  /// The parameter `kosyu_category_code` of this provider.
  String get kosyu_category_code;

  /// The parameter `freeword` of this provider.
  String? get freeword;

  /// The parameter `start_date` of this provider.
  String get start_date;

  /// The parameter `end_date` of this provider.
  String get end_date;
}

class _FetchCourseProviderElement
    extends AutoDisposeFutureProviderElement<List<dynamic>>
    with FetchCourseRef {
  _FetchCourseProviderElement(super.provider);

  @override
  String get jimusyo_code => (origin as FetchCourseProvider).jimusyo_code;
  @override
  String get kosyu_category_code =>
      (origin as FetchCourseProvider).kosyu_category_code;
  @override
  String? get freeword => (origin as FetchCourseProvider).freeword;
  @override
  String get start_date => (origin as FetchCourseProvider).start_date;
  @override
  String get end_date => (origin as FetchCourseProvider).end_date;
}

String _$fetchCourseGuideHash() => r'a6d02ebb15071e547d7663ecfd6254b87ef0b505';

/// See also [fetchCourseGuide].
@ProviderFor(fetchCourseGuide)
const fetchCourseGuideProvider = FetchCourseGuideFamily();

/// See also [fetchCourseGuide].
class FetchCourseGuideFamily extends Family<AsyncValue<List<CourseGuide>>> {
  /// See also [fetchCourseGuide].
  const FetchCourseGuideFamily();

  /// See also [fetchCourseGuide].
  FetchCourseGuideProvider call(
    String koushuu_code,
  ) {
    return FetchCourseGuideProvider(
      koushuu_code,
    );
  }

  @override
  FetchCourseGuideProvider getProviderOverride(
    covariant FetchCourseGuideProvider provider,
  ) {
    return call(
      provider.koushuu_code,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchCourseGuideProvider';
}

/// See also [fetchCourseGuide].
class FetchCourseGuideProvider
    extends AutoDisposeFutureProvider<List<CourseGuide>> {
  /// See also [fetchCourseGuide].
  FetchCourseGuideProvider(
    String koushuu_code,
  ) : this._internal(
          (ref) => fetchCourseGuide(
            ref as FetchCourseGuideRef,
            koushuu_code,
          ),
          from: fetchCourseGuideProvider,
          name: r'fetchCourseGuideProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchCourseGuideHash,
          dependencies: FetchCourseGuideFamily._dependencies,
          allTransitiveDependencies:
              FetchCourseGuideFamily._allTransitiveDependencies,
          koushuu_code: koushuu_code,
        );

  FetchCourseGuideProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.koushuu_code,
  }) : super.internal();

  final String koushuu_code;

  @override
  Override overrideWith(
    FutureOr<List<CourseGuide>> Function(FetchCourseGuideRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchCourseGuideProvider._internal(
        (ref) => create(ref as FetchCourseGuideRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        koushuu_code: koushuu_code,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<CourseGuide>> createElement() {
    return _FetchCourseGuideProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchCourseGuideProvider &&
        other.koushuu_code == koushuu_code;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, koushuu_code.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchCourseGuideRef on AutoDisposeFutureProviderRef<List<CourseGuide>> {
  /// The parameter `koushuu_code` of this provider.
  String get koushuu_code;
}

class _FetchCourseGuideProviderElement
    extends AutoDisposeFutureProviderElement<List<CourseGuide>>
    with FetchCourseGuideRef {
  _FetchCourseGuideProviderElement(super.provider);

  @override
  String get koushuu_code => (origin as FetchCourseGuideProvider).koushuu_code;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
