import 'dart:convert';
import 'package:collection/collection.dart'; // groupBy関数のため
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:koushuu_system/core/api_service.dart';
import 'package:koushuu_system/core/env_config.dart';
import 'package:koushuu_system/models/course.dart';
import 'package:koushuu_system/models/course_guide.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:koushuu_system/core/constants.dart' as constants;

part 'course_repository.g.dart';

class CourseRepository {
  Future<List<CourseGuide>> getKoushuuGuide(String kosyu_code) async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/getkoushuuguide";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
    };

    final params = {
      "kosyu_code": kosyu_code,
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);

    Response postResponse = await apiService.postRequest(
      strPath,
      data: params,
    );

    var jsonData = json.decode(postResponse.toString());

    if (jsonData["status"] == constants.API_RES_SUCCESS) {
      Iterable l = jsonData["responsedata"]["course_list"];
      List<CourseGuide> lst =
          List<CourseGuide>.from(l.map((model) => CourseGuide.fromJson(model)));

      // final formattedJson =
      //const JsonEncoder.withIndent('  ').convert(resultFinal);
      // print(formattedJson);

      return lst;
    } else {
      return [];
    }
  }

  Future<List<dynamic>> fetchData(
    String jimusyo_code,
    String kosyu_category_code,
    String? freeword,
    String start_date,
    String end_date,
  ) async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/getkoushuulist";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
    };

    final params = {
      "jimusyo_code": jimusyo_code,
      "kosyu_category_code": kosyu_category_code,
      "freeword": freeword,
      "start_date": start_date,
      "end_date": end_date,
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);

    Response postResponse = await apiService.postRequest(
      strPath,
      data: {},
      queryParameters: params,
    );

    var jsonData = json.decode(postResponse.toString());

    if (jsonData["status"] == constants.API_RES_SUCCESS) {
      Iterable l = jsonData["responsedata"]["kosyu_list"];
      List<Course> lst =
          List<Course>.from(l.map((model) => Course.fromJson(model)));

      // final result = lst
      //     .whereWithIndex((element, index) =>
      //         lst.indexWhere(
      //             (element2) => element2.course_code == element.course_code) ==
      //         index)
      //     .toList();

      print("^^-^-^-^-^-^-^-^^^--^");
      print(lst.toString());
      print("^^-^-^-^-^-^-^-^^^--^");
      // kosyu_code と kosyu_omit を1つのキーに連結してグループ化
      final groupedByKosyu = groupBy(
        lst,
        (Course item) =>
            '${item.kosyu_code}_${item.kosyu_name}_${item.kosyu_number}_${item.subtitle}',
      );

      // グループを統合
      final List<Map<String, dynamic>> resultFinal =
          groupedByKosyu.entries.map((entry) {
        final key = entry.key
            .split('_'); // 分割して kosyu_code と kosyu_omit と kosyuNumberに戻す
        final kosyuCode = key[0];
        final kosyuOmit = key[1];
        final kosyuNumber = key[2];
        final subtitle = key[3];
        final groupItems = entry.value;

        // 各 kosyu_code 内で kaijyo_data を統合
        final Map<String, Map<String, dynamic>> mergedKaijyoData = {};

        for (var item in groupItems) {
          final kaijyoCode = item.kaijyo_code;

          if (!mergedKaijyoData.containsKey(kaijyoCode)) {
            mergedKaijyoData[kaijyoCode] = {
              'kaijyo_code': kaijyoCode,
              'kaijyo_name': item.kaijyo_name,
              'jimusyo_code': item.jimusyo_code,
              'jimusyo_name': item.jimusyo_name,
              // 'course_name': item.course_name,
              // 'course_price': item.price,
              // 'course_code': item.course_code,
              'attend_days_list': List<AttendDay>.from(item.attend_days_list),
            };
          } else {
            // attend_days_list をマージ
            mergedKaijyoData[kaijyoCode]!['attend_days_list'].addAll(
              List<AttendDay>.from(item.attend_days_list),
            );
          }
        }

        print("mergeddatat=-------------------------");
        print(mergedKaijyoData.toString());
        print("mergeddatat=-------------------------");

        // kaijyo_data を月ごとにグループ化
        final List<Map<String, dynamic>> kaijyoDataWithMonthlyGrouping =
            mergedKaijyoData.values.map((kaijyo) {
          final monthlyGroupedAttendDays = groupBy(
            kaijyo['attend_days_list'],
            (AttendDay attendDay) {
              DateFormat inputFormat = DateFormat("yyyy/MM/dd");
              final startDate = inputFormat.parse(attendDay.start_date);
              return startDate.month; // 月をグループキーに
            },
          );

          // グループ化された attend_days_list を再構築
          final groupedAttendDaysList =
              monthlyGroupedAttendDays.entries.map((entry) {
            return {
              'month': '${entry.key}月', // 月
              'attend_days': entry.value, // グループ化されたリスト
            };
          }).toList();

          return {
            'kaijyo_code': kaijyo['kaijyo_code'],
            'kaijyo_name': kaijyo['kaijyo_name'],
            'jimusyo_code': kaijyo['jimusyo_code'],
            'jimusyo_name': kaijyo['jimusyo_name'],
            // 'course_name': kaijyo['course_name'],
            // 'course_price': kaijyo['course_price'].toString(),
            // 'course_code': kaijyo['course_code'],
            'attend_days_list_by_month':
                groupedAttendDaysList, // 月ごとの attend_days_list
          };
        }).toList();

        return {
          'kosyu_code': kosyuCode,
          'kosyu_omit': kosyuOmit,
          'kosyu_number': kosyuNumber,
          'subtitle': subtitle,
          'kaijyo_data': kaijyoDataWithMonthlyGrouping,
        };
      }).toList();

      // final formattedJson =
      //     const JsonEncoder.withIndent('  ').convert(resultFinal);
      // print(formattedJson);

      return resultFinal;
    } else {
      return [];
    }
  }
}

// this will generate a CourseRepositoryProvider
@riverpod
CourseRepository courseRepository(Ref ref) {
  return CourseRepository();
}

@riverpod
Future<List<dynamic>> fetchCourse(
  Ref ref,
  String jimusyo_code,
  String kosyu_category_code,
  String? freeword,
  String start_date,
  String end_date,
) {
  return ref.watch(courseRepositoryProvider).fetchData(
      jimusyo_code, kosyu_category_code, freeword, start_date, end_date);
}

@riverpod
Future<List<CourseGuide>> fetchCourseGuide(Ref ref, String koushuu_code) {
  return ref.watch(courseRepositoryProvider).getKoushuuGuide(koushuu_code);
}
