import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:koushuu_system/core/api_service.dart';
import 'package:koushuu_system/core/env_config.dart';
import 'package:koushuu_system/extensions/extensions.dart';
import 'package:koushuu_system/models/pref.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'pref_repository.g.dart';

class PrefRepository {
  Future<List<Pref>> fetchData() async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/getpreflist";

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint);

    Response postResponse = await apiService.postRequest(
      strPath,
      data: {},
    );

    var jsonData = json.decode(postResponse.toString());

    Iterable l = jsonData["responsedata"]["pref_list"];
    List<Pref> lst = List<Pref>.from(l.map((model) => Pref.fromJson(model)));

    final result = lst
        .whereWithIndex((element, index) =>
            lst.indexWhere(
                (element2) => element2.jimusyo_code == element.jimusyo_code) ==
            index)
        .toList();

    return result;
  }
}

// this will generate a PrefRepositoryProvider
@riverpod
PrefRepository prefRepository(Ref ref) {
  return PrefRepository();
}

@riverpod
Future<List<Pref>> fetchPref(Ref ref) {
  return ref.watch(prefRepositoryProvider).fetchData();
}
