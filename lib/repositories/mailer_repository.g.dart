// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'mailer_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$mailerRepositoryHash() => r'f1eed79873d8b0a903fd179d404a8b85d2f77151';

/// See also [mailerRepository].
@ProviderFor(mailerRepository)
final mailerRepositoryProvider = AutoDisposeProvider<MailerRepository>.internal(
  mailerRepository,
  name: r'mailerRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$mailerRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef MailerRepositoryRef = AutoDisposeProviderRef<MailerRepository>;
String _$sendEmailHash() => r'78be08aa815e1013a1f3c2e8015c356108894358';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [sendEmail].
@ProviderFor(sendEmail)
const sendEmailProvider = SendEmailFamily();

/// See also [sendEmail].
class SendEmailFamily extends Family<AsyncValue> {
  /// See also [sendEmail].
  const SendEmailFamily();

  /// See also [sendEmail].
  SendEmailProvider call(
    String to,
    String subject,
    String templateId,
    Map<String, dynamic> extraAttributes,
  ) {
    return SendEmailProvider(
      to,
      subject,
      templateId,
      extraAttributes,
    );
  }

  @override
  SendEmailProvider getProviderOverride(
    covariant SendEmailProvider provider,
  ) {
    return call(
      provider.to,
      provider.subject,
      provider.templateId,
      provider.extraAttributes,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'sendEmailProvider';
}

/// See also [sendEmail].
class SendEmailProvider extends AutoDisposeFutureProvider<Object?> {
  /// See also [sendEmail].
  SendEmailProvider(
    String to,
    String subject,
    String templateId,
    Map<String, dynamic> extraAttributes,
  ) : this._internal(
          (ref) => sendEmail(
            ref as SendEmailRef,
            to,
            subject,
            templateId,
            extraAttributes,
          ),
          from: sendEmailProvider,
          name: r'sendEmailProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$sendEmailHash,
          dependencies: SendEmailFamily._dependencies,
          allTransitiveDependencies: SendEmailFamily._allTransitiveDependencies,
          to: to,
          subject: subject,
          templateId: templateId,
          extraAttributes: extraAttributes,
        );

  SendEmailProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.to,
    required this.subject,
    required this.templateId,
    required this.extraAttributes,
  }) : super.internal();

  final String to;
  final String subject;
  final String templateId;
  final Map<String, dynamic> extraAttributes;

  @override
  Override overrideWith(
    FutureOr<Object?> Function(SendEmailRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SendEmailProvider._internal(
        (ref) => create(ref as SendEmailRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        to: to,
        subject: subject,
        templateId: templateId,
        extraAttributes: extraAttributes,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Object?> createElement() {
    return _SendEmailProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SendEmailProvider &&
        other.to == to &&
        other.subject == subject &&
        other.templateId == templateId &&
        other.extraAttributes == extraAttributes;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, to.hashCode);
    hash = _SystemHash.combine(hash, subject.hashCode);
    hash = _SystemHash.combine(hash, templateId.hashCode);
    hash = _SystemHash.combine(hash, extraAttributes.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SendEmailRef on AutoDisposeFutureProviderRef<Object?> {
  /// The parameter `to` of this provider.
  String get to;

  /// The parameter `subject` of this provider.
  String get subject;

  /// The parameter `templateId` of this provider.
  String get templateId;

  /// The parameter `extraAttributes` of this provider.
  Map<String, dynamic> get extraAttributes;
}

class _SendEmailProviderElement
    extends AutoDisposeFutureProviderElement<Object?> with SendEmailRef {
  _SendEmailProviderElement(super.provider);

  @override
  String get to => (origin as SendEmailProvider).to;
  @override
  String get subject => (origin as SendEmailProvider).subject;
  @override
  String get templateId => (origin as SendEmailProvider).templateId;
  @override
  Map<String, dynamic> get extraAttributes =>
      (origin as SendEmailProvider).extraAttributes;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
