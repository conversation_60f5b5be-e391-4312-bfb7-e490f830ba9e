// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userRepositoryHash() => r'8366fba5ac0d6b90c6a637882d24c5e759a5a92f';

/// See also [userRepository].
@ProviderFor(userRepository)
final userRepositoryProvider = AutoDisposeProvider<UserRepository>.internal(
  userRepository,
  name: r'userRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef UserRepositoryRef = AutoDisposeProviderRef<UserRepository>;
String _$checkUserHash() => r'b48459ae998b59f1709790b89a3aeb526091720b';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [checkUser].
@ProviderFor(checkUser)
const checkUserProvider = CheckUserFamily();

/// See also [checkUser].
class CheckUserFamily extends Family<AsyncValue<dynamic>> {
  /// See also [checkUser].
  const CheckUserFamily();

  /// See also [checkUser].
  CheckUserProvider call(
    String email,
    String applicationId,
  ) {
    return CheckUserProvider(
      email,
      applicationId,
    );
  }

  @override
  CheckUserProvider getProviderOverride(
    covariant CheckUserProvider provider,
  ) {
    return call(
      provider.email,
      provider.applicationId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'checkUserProvider';
}

/// See also [checkUser].
class CheckUserProvider extends AutoDisposeFutureProvider<dynamic> {
  /// See also [checkUser].
  CheckUserProvider(
    String email,
    String applicationId,
  ) : this._internal(
          (ref) => checkUser(
            ref as CheckUserRef,
            email,
            applicationId,
          ),
          from: checkUserProvider,
          name: r'checkUserProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$checkUserHash,
          dependencies: CheckUserFamily._dependencies,
          allTransitiveDependencies: CheckUserFamily._allTransitiveDependencies,
          email: email,
          applicationId: applicationId,
        );

  CheckUserProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.email,
    required this.applicationId,
  }) : super.internal();

  final String email;
  final String applicationId;

  @override
  Override overrideWith(
    FutureOr<dynamic> Function(CheckUserRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CheckUserProvider._internal(
        (ref) => create(ref as CheckUserRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        email: email,
        applicationId: applicationId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<dynamic> createElement() {
    return _CheckUserProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CheckUserProvider &&
        other.email == email &&
        other.applicationId == applicationId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, email.hashCode);
    hash = _SystemHash.combine(hash, applicationId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CheckUserRef on AutoDisposeFutureProviderRef<dynamic> {
  /// The parameter `email` of this provider.
  String get email;

  /// The parameter `applicationId` of this provider.
  String get applicationId;
}

class _CheckUserProviderElement
    extends AutoDisposeFutureProviderElement<dynamic> with CheckUserRef {
  _CheckUserProviderElement(super.provider);

  @override
  String get email => (origin as CheckUserProvider).email;
  @override
  String get applicationId => (origin as CheckUserProvider).applicationId;
}

String _$setPasswordHash() => r'21d287899a66b63816e2969ef1c6aa4debaf3298';

/// See also [setPassword].
@ProviderFor(setPassword)
const setPasswordProvider = SetPasswordFamily();

/// See also [setPassword].
class SetPasswordFamily extends Family<AsyncValue<dynamic>> {
  /// See also [setPassword].
  const SetPasswordFamily();

  /// See also [setPassword].
  SetPasswordProvider call(
    String email,
    String pass,
    String applicationId,
  ) {
    return SetPasswordProvider(
      email,
      pass,
      applicationId,
    );
  }

  @override
  SetPasswordProvider getProviderOverride(
    covariant SetPasswordProvider provider,
  ) {
    return call(
      provider.email,
      provider.pass,
      provider.applicationId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'setPasswordProvider';
}

/// See also [setPassword].
class SetPasswordProvider extends AutoDisposeFutureProvider<dynamic> {
  /// See also [setPassword].
  SetPasswordProvider(
    String email,
    String pass,
    String applicationId,
  ) : this._internal(
          (ref) => setPassword(
            ref as SetPasswordRef,
            email,
            pass,
            applicationId,
          ),
          from: setPasswordProvider,
          name: r'setPasswordProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$setPasswordHash,
          dependencies: SetPasswordFamily._dependencies,
          allTransitiveDependencies:
              SetPasswordFamily._allTransitiveDependencies,
          email: email,
          pass: pass,
          applicationId: applicationId,
        );

  SetPasswordProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.email,
    required this.pass,
    required this.applicationId,
  }) : super.internal();

  final String email;
  final String pass;
  final String applicationId;

  @override
  Override overrideWith(
    FutureOr<dynamic> Function(SetPasswordRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SetPasswordProvider._internal(
        (ref) => create(ref as SetPasswordRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        email: email,
        pass: pass,
        applicationId: applicationId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<dynamic> createElement() {
    return _SetPasswordProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SetPasswordProvider &&
        other.email == email &&
        other.pass == pass &&
        other.applicationId == applicationId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, email.hashCode);
    hash = _SystemHash.combine(hash, pass.hashCode);
    hash = _SystemHash.combine(hash, applicationId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SetPasswordRef on AutoDisposeFutureProviderRef<dynamic> {
  /// The parameter `email` of this provider.
  String get email;

  /// The parameter `pass` of this provider.
  String get pass;

  /// The parameter `applicationId` of this provider.
  String get applicationId;
}

class _SetPasswordProviderElement
    extends AutoDisposeFutureProviderElement<dynamic> with SetPasswordRef {
  _SetPasswordProviderElement(super.provider);

  @override
  String get email => (origin as SetPasswordProvider).email;
  @override
  String get pass => (origin as SetPasswordProvider).pass;
  @override
  String get applicationId => (origin as SetPasswordProvider).applicationId;
}

String _$loginHash() => r'563c9b789328a0185f6f5776276906d43d9fe272';

/// See also [login].
@ProviderFor(login)
const loginProvider = LoginFamily();

/// See also [login].
class LoginFamily extends Family<AsyncValue<dynamic>> {
  /// See also [login].
  const LoginFamily();

  /// See also [login].
  LoginProvider call(
    String email,
    String pass,
    String applicationId,
  ) {
    return LoginProvider(
      email,
      pass,
      applicationId,
    );
  }

  @override
  LoginProvider getProviderOverride(
    covariant LoginProvider provider,
  ) {
    return call(
      provider.email,
      provider.pass,
      provider.applicationId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'loginProvider';
}

/// See also [login].
class LoginProvider extends AutoDisposeFutureProvider<dynamic> {
  /// See also [login].
  LoginProvider(
    String email,
    String pass,
    String applicationId,
  ) : this._internal(
          (ref) => login(
            ref as LoginRef,
            email,
            pass,
            applicationId,
          ),
          from: loginProvider,
          name: r'loginProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$loginHash,
          dependencies: LoginFamily._dependencies,
          allTransitiveDependencies: LoginFamily._allTransitiveDependencies,
          email: email,
          pass: pass,
          applicationId: applicationId,
        );

  LoginProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.email,
    required this.pass,
    required this.applicationId,
  }) : super.internal();

  final String email;
  final String pass;
  final String applicationId;

  @override
  Override overrideWith(
    FutureOr<dynamic> Function(LoginRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: LoginProvider._internal(
        (ref) => create(ref as LoginRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        email: email,
        pass: pass,
        applicationId: applicationId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<dynamic> createElement() {
    return _LoginProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is LoginProvider &&
        other.email == email &&
        other.pass == pass &&
        other.applicationId == applicationId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, email.hashCode);
    hash = _SystemHash.combine(hash, pass.hashCode);
    hash = _SystemHash.combine(hash, applicationId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin LoginRef on AutoDisposeFutureProviderRef<dynamic> {
  /// The parameter `email` of this provider.
  String get email;

  /// The parameter `pass` of this provider.
  String get pass;

  /// The parameter `applicationId` of this provider.
  String get applicationId;
}

class _LoginProviderElement extends AutoDisposeFutureProviderElement<dynamic>
    with LoginRef {
  _LoginProviderElement(super.provider);

  @override
  String get email => (origin as LoginProvider).email;
  @override
  String get pass => (origin as LoginProvider).pass;
  @override
  String get applicationId => (origin as LoginProvider).applicationId;
}

String _$resetPasswordHash() => r'b114f15c6f5ae10e30004abad975cdfbe22fffa7';

/// See also [resetPassword].
@ProviderFor(resetPassword)
const resetPasswordProvider = ResetPasswordFamily();

/// See also [resetPassword].
class ResetPasswordFamily extends Family<AsyncValue<dynamic>> {
  /// See also [resetPassword].
  const ResetPasswordFamily();

  /// See also [resetPassword].
  ResetPasswordProvider call(
    String email,
    int applicationId,
  ) {
    return ResetPasswordProvider(
      email,
      applicationId,
    );
  }

  @override
  ResetPasswordProvider getProviderOverride(
    covariant ResetPasswordProvider provider,
  ) {
    return call(
      provider.email,
      provider.applicationId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'resetPasswordProvider';
}

/// See also [resetPassword].
class ResetPasswordProvider extends AutoDisposeFutureProvider<dynamic> {
  /// See also [resetPassword].
  ResetPasswordProvider(
    String email,
    int applicationId,
  ) : this._internal(
          (ref) => resetPassword(
            ref as ResetPasswordRef,
            email,
            applicationId,
          ),
          from: resetPasswordProvider,
          name: r'resetPasswordProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$resetPasswordHash,
          dependencies: ResetPasswordFamily._dependencies,
          allTransitiveDependencies:
              ResetPasswordFamily._allTransitiveDependencies,
          email: email,
          applicationId: applicationId,
        );

  ResetPasswordProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.email,
    required this.applicationId,
  }) : super.internal();

  final String email;
  final int applicationId;

  @override
  Override overrideWith(
    FutureOr<dynamic> Function(ResetPasswordRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ResetPasswordProvider._internal(
        (ref) => create(ref as ResetPasswordRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        email: email,
        applicationId: applicationId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<dynamic> createElement() {
    return _ResetPasswordProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ResetPasswordProvider &&
        other.email == email &&
        other.applicationId == applicationId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, email.hashCode);
    hash = _SystemHash.combine(hash, applicationId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ResetPasswordRef on AutoDisposeFutureProviderRef<dynamic> {
  /// The parameter `email` of this provider.
  String get email;

  /// The parameter `applicationId` of this provider.
  int get applicationId;
}

class _ResetPasswordProviderElement
    extends AutoDisposeFutureProviderElement<dynamic> with ResetPasswordRef {
  _ResetPasswordProviderElement(super.provider);

  @override
  String get email => (origin as ResetPasswordProvider).email;
  @override
  int get applicationId => (origin as ResetPasswordProvider).applicationId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
