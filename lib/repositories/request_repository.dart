import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:koushuu_system/core/api_service.dart';
import 'package:koushuu_system/core/env_config.dart';
import 'package:koushuu_system/models/rerequest.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:koushuu_system/core/constants.dart' as constants;

part 'request_repository.g.dart';

class RequestRepository {
  //First step for open new request
  Future<dynamic> openRequest(dynamic req) async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/openrequest";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);

    Response postResponse = await apiService.postRequest(
      strPath,
      data: {
        "applicant_id": req["applicant_id"],
        "applicant_name": req["applicant_name"],
        "applicant_tel": req["applicant_tel"],
        "applicant_fax": req["applicant_fax"],
        "applicant_addr": req["applicant_addr"],
        "email": req["email"],
        "subject": "申請者パスワードの再設定",
        "people_cnt": req["people_cnt"],
        "extra_attributes": req["extra_attributes"],
        "kousyu_code": req["kousyu_code"],
        "kousyu_number": req["kousyu_number"],
        "kousyu_name": req["kousyu_name"],
        "kousyu_date": req["kousyu_date"],
        "jimusyo_code": req["jimusyo_code"],
        "jimusyo_name": req["jimusyo_name"],
        "course_code": req["course_code"],
      },
    );
    var jsonData = json.decode(postResponse.toString());
    return jsonData;
  }

  Future<dynamic> closeRequest(String applicationId) async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/closerequest";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);

    Response postResponse = await apiService.postRequest(
      strPath,
      data: {
        "application_id": applicationId,
      },
    );
    var jsonData = json.decode(postResponse.toString());
    return jsonData;
  }

  //再発行依頼作成
  Future<dynamic> makeReRequest(ReRequest req) async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/makesaihakkoirai";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);

    Response postResponse = await apiService.postRequest(
      strPath,
      data: req.toJson(),
      // {
      //   "uketsuke_id": req.uketsuke_id,
      //   "name_sei": req.name_set,
      //   "name_mei": req.nameMei,
      //   "name_kana": req.nameKana,
      //   "old_name": req.oldName,
      //   "old_name_kana": req.oldNameKana,
      //   "birth_day": req.birthDay,
      //   "zip_code": req.zipCode,
      //   "addr1": req.addr1,
      //   "addr2": req.addr2,
      //   "tel_no": req.telNo,
      //   "mail_addr": req.mailAddr,
      //   "kosyu_type": req.kosyuType,
      //   "jimusyo_code": req.jimusyoCode,
      //   "lost_flag": req.lostFlag,
      //   "change_name_flag": req.changeNameFlag,
      //   "damage_flag": req.damageFlag,
      //   "other_flag": req.otherFlag,
      //   "other_riyu": req.otherRiyu,
      //   "comment": req.comment,
      //   "license": req.license,
      //   "place": req.place,
      //   "reason_title": req.reasonTitle,
      // },
    );

    var jsonData = json.decode(postResponse.toString());
    return jsonData;
  }

  //再発依頼ロード
  Future<Map<String, dynamic>> readReRequest(String uketsukeid) async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/readsaihakkoirai";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);

    Response postResponse = await apiService.postRequest(
      strPath,
      data: {
        "uketsuke_id": uketsukeid,
      },
    );
    var jsonData = json.decode(postResponse.toString());
    return jsonData;
  }

  //再発行受付作成
  Future<dynamic> makeReRequestUketsuke(ReRequestDoc req) async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/makesaihakkouketsuke";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);

    Response postResponse = await apiService.postRequest(
      strPath,
      data: req.toJson(),
    );

    var jsonData = json.decode(postResponse.toString());
    return jsonData;
  }

  //再発行受付ロード
  Future<Map<String, dynamic>> readReRequestUketsuke(String uketsukeid) async {
    final iraiData = await readReRequest(uketsukeid);

    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/readsaihakkouketsuke";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);

    Response postResponse = await apiService.postRequest(
      strPath,
      data: {
        "uketsuke_id": uketsukeid,
      },
    );
    var jsonData = json.decode(postResponse.toString());
    print(iraiData["responsedata"]);
    if (iraiData["status"] == constants.API_RES_SUCCESS) {
      jsonData["responsedata"]["irai_data"] = iraiData["responsedata"];
    } else {
      jsonData["responsedata"]["irai_data"] = null;
    }
    return jsonData;
  }
}

// this will generate a RequestRepositoryProvider
@riverpod
RequestRepository requestRepository(Ref ref) {
  return RequestRepository();
}

@riverpod
Future<Map<String, dynamic>> readReRequest(Ref ref, String uketsukeid) {
  return ref.watch(requestRepositoryProvider).readReRequest(uketsukeid);
}

@riverpod
Future makeReRequest(Ref ref, ReRequest req) {
  return ref.watch(requestRepositoryProvider).makeReRequest(req);
}

@riverpod
Future<Map<String, dynamic>> readReRequestUketsuke(Ref ref, String uketsukeid) {
  return ref.watch(requestRepositoryProvider).readReRequestUketsuke(uketsukeid);
}

@riverpod
Future makeReRequestUketsuke(Ref ref, ReRequestDoc req) {
  return ref.watch(requestRepositoryProvider).makeReRequestUketsuke(req);
}

@riverpod
Future closeRequest(Ref ref, dynamic req) {
  return ref.watch(requestRepositoryProvider).closeRequest(req);
}

@riverpod
Future openRequest(Ref ref, dynamic req) {
  return ref.watch(requestRepositoryProvider).openRequest(req);
}
