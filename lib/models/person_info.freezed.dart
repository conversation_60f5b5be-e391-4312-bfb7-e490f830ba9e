// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'person_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PersonInfo _$PersonInfoFromJson(Map<String, dynamic> json) {
  return _PersonInfo.fromJson(json);
}

/// @nodoc
mixin _$PersonInfo {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get uketsukeId => throw _privateConstructorUsedError;
  String get status => throw _privateConstructorUsedError;

  /// Serializes this PersonInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PersonInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PersonInfoCopyWith<PersonInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PersonInfoCopyWith<$Res> {
  factory $PersonInfoCopyWith(
          PersonInfo value, $Res Function(PersonInfo) then) =
      _$PersonInfoCopyWithImpl<$Res, PersonInfo>;
  @useResult
  $Res call(
      {String id, String name, String email, String uketsukeId, String status});
}

/// @nodoc
class _$PersonInfoCopyWithImpl<$Res, $Val extends PersonInfo>
    implements $PersonInfoCopyWith<$Res> {
  _$PersonInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PersonInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? email = null,
    Object? uketsukeId = null,
    Object? status = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      uketsukeId: null == uketsukeId
          ? _value.uketsukeId
          : uketsukeId // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PersonInfoImplCopyWith<$Res>
    implements $PersonInfoCopyWith<$Res> {
  factory _$$PersonInfoImplCopyWith(
          _$PersonInfoImpl value, $Res Function(_$PersonInfoImpl) then) =
      __$$PersonInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id, String name, String email, String uketsukeId, String status});
}

/// @nodoc
class __$$PersonInfoImplCopyWithImpl<$Res>
    extends _$PersonInfoCopyWithImpl<$Res, _$PersonInfoImpl>
    implements _$$PersonInfoImplCopyWith<$Res> {
  __$$PersonInfoImplCopyWithImpl(
      _$PersonInfoImpl _value, $Res Function(_$PersonInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of PersonInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? email = null,
    Object? uketsukeId = null,
    Object? status = null,
  }) {
    return _then(_$PersonInfoImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      uketsukeId: null == uketsukeId
          ? _value.uketsukeId
          : uketsukeId // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PersonInfoImpl implements _PersonInfo {
  const _$PersonInfoImpl(
      {required this.id,
      required this.name,
      required this.email,
      required this.uketsukeId,
      required this.status});

  factory _$PersonInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$PersonInfoImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String email;
  @override
  final String uketsukeId;
  @override
  final String status;

  @override
  String toString() {
    return 'PersonInfo(id: $id, name: $name, email: $email, uketsukeId: $uketsukeId, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PersonInfoImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.uketsukeId, uketsukeId) ||
                other.uketsukeId == uketsukeId) &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, name, email, uketsukeId, status);

  /// Create a copy of PersonInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PersonInfoImplCopyWith<_$PersonInfoImpl> get copyWith =>
      __$$PersonInfoImplCopyWithImpl<_$PersonInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PersonInfoImplToJson(
      this,
    );
  }
}

abstract class _PersonInfo implements PersonInfo {
  const factory _PersonInfo(
      {required final String id,
      required final String name,
      required final String email,
      required final String uketsukeId,
      required final String status}) = _$PersonInfoImpl;

  factory _PersonInfo.fromJson(Map<String, dynamic> json) =
      _$PersonInfoImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get email;
  @override
  String get uketsukeId;
  @override
  String get status;

  /// Create a copy of PersonInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PersonInfoImplCopyWith<_$PersonInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
