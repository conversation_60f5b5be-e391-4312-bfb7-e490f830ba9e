// This file is "main.dart"
import 'package:freezed_annotation/freezed_annotation.dart';

part 'participant.freezed.dart';
part 'participant.g.dart';

@freezed
class Participant with _$Participant {
  const factory Participant({
    int? id,
    int? application_id, // 申込ID
    String? uketsuke_id, // 受付ID
    String? kosyu_number, // 講習番号
    int? kosyu_code, // 講習コード
    String? kosyu_date, // 講習日付
    int? course_code, // コースコード
    int? jimusyo_code, // 事務所コード
    int? kaijyo_code, // 会場コード
    String? applicant_number, // 申込番号
    String? uketsuke_date, // 申込番号
    int? applicant_code, // 申込番号
    String? name1, // 氏名1
    String? name2, // 氏名2
    String? name_kana, // 氏名カナ
    int? old_or_common_name_type, // 旧姓通称区分
    String? reason, // 旧姓通称区分
    String? name3, // 氏名3
    String? birth_day, // 生年月日
    String? zip_code, // 郵便番号
    String? addr1, // 現住所1
    String? addr2, // 現住所2
    String? tel_no, // 電話番号
    String? mail_addr, // メールアドレス
    int? opt_company,
    String? unacquired_kosyu_code, // 未取得講習コード
    String? declaration_party_name, // 申告団体名
    String? declaration_date, // 申告交付日付
    int? soufu_kubun, // 送付先区分
    String? url, // 入力フォームURL
    String? image_photo, // 本人写真画像データ(Base64)
    String? filename_photo, // 本人写真画像データ(Base64)
    String? filetype_photo, // 本人写真画像データ(Base64)
    String? image_kakunin_omote, // 本人確認(表)画像データ(Base64)
    String? filename_kakunin_omote, // 本人確認(表)画像データ(Base64)
    String? filetype_kakunin_omote, // 本人確認(表)画像データ(Base64)
    String? image_kakunin_ura, // 本人確認(裏)画像データ(Base64)
    String? filename_kakunin_ura, // 本人確認(裏)画像データ(Base64)
    String? filetype_kakunin_ura, // 本人確認(裏)画像データ(Base64)
    String? image_kakunin_atsumi, // 本人確認(厚み)画像データ(Base64)
    String? filename_kakunin_atsumi, // 本人確認(厚み)画像データ(Base64)
    String? filetype_kakunin_atsumi, // 本人確認(厚み)画像データ(Base64)
    String? image_license1, // 受講資格1画像データ(Base64)
    String? filename_license1, // 受講資格1画像データ(Base64)
    String? filetype_license1, // 受講資格1画像データ(Base64)
    String? image_license2, // 受講資格2画像データ(Base64)
    String? filename_license2, // 受講資格2画像データ(Base64)
    String? filetype_license2, // 受講資格2画像データ(Base64)
    String? image_license3, // 受講資格3画像データ(Base64)
    String? filename_license3, // 受講資格3画像データ(Base64)
    String? filetype_license3, // 受講資格3画像データ(Base64)
    String? sinkoku_jimusyo_code,
    int? sinkoku_kosyu_kubun,
    String? sinkoku_kosyu_date,
    int? sinkoku_kuwari_kubun,
    String? unacquired_kosyu_code_title,
    String? sinkoku_jimusyo_code_title,
    String? sinkoku_kosyu_kubun_title,
    String? sinkoku_kuwari_kubun_title,
  }) = _Participant;

  factory Participant.fromJson(Map<String, Object?> json) =>
      _$ParticipantFromJson(json);
}
