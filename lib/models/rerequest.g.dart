// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rerequest.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ReRequestImpl _$$ReRequestImplFromJson(Map<String, dynamic> json) =>
    _$ReRequestImpl(
      uketsuke_id: json['uketsuke_id'] as String?,
      name_sei: json['name_sei'] as String?,
      name_mei: json['name_mei'] as String?,
      name_name_kana: json['name_name_kana'] as String?,
      old_name: json['old_name'] as String?,
      old_name_kana: json['old_name_kana'] as String?,
      birth_day: json['birth_day'] as String?,
      zip_code: json['zip_code'] as String?,
      addr1: json['addr1'] as String?,
      addr2: json['addr2'] as String?,
      tel_no: json['tel_no'] as String?,
      mail_addr: json['mail_addr'] as String?,
      kosyu_type: json['kosyu_type'] as String?,
      jimusyo_code: json['jimusyo_code'] as String?,
      lost_flag: json['lost_flag'] as String?,
      change_name_flag: json['change_name_flag'] as String?,
      damage_flag: json['damage_flag'] as String?,
      other_flag: json['other_flag'] as String?,
      other_riyu: json['other_riyu'] as String?,
      comment: json['comment'] as String?,
      url: json['url'] as String?,
      license: json['license'] as String?,
      reason_title: json['reason_title'] as String?,
      place: json['place'] as String?,
    );

Map<String, dynamic> _$$ReRequestImplToJson(_$ReRequestImpl instance) =>
    <String, dynamic>{
      'uketsuke_id': instance.uketsuke_id,
      'name_sei': instance.name_sei,
      'name_mei': instance.name_mei,
      'name_name_kana': instance.name_name_kana,
      'old_name': instance.old_name,
      'old_name_kana': instance.old_name_kana,
      'birth_day': instance.birth_day,
      'zip_code': instance.zip_code,
      'addr1': instance.addr1,
      'addr2': instance.addr2,
      'tel_no': instance.tel_no,
      'mail_addr': instance.mail_addr,
      'kosyu_type': instance.kosyu_type,
      'jimusyo_code': instance.jimusyo_code,
      'lost_flag': instance.lost_flag,
      'change_name_flag': instance.change_name_flag,
      'damage_flag': instance.damage_flag,
      'other_flag': instance.other_flag,
      'other_riyu': instance.other_riyu,
      'comment': instance.comment,
      'url': instance.url,
      'license': instance.license,
      'reason_title': instance.reason_title,
      'place': instance.place,
    };

_$ReRequestDocImpl _$$ReRequestDocImplFromJson(Map<String, dynamic> json) =>
    _$ReRequestDocImpl(
      uketsuke_id: json['uketsuke_id'] as String?,
      name: json['name'] as String?,
      name_kana: json['name_kana'] as String?,
      birth_day: json['birth_day'] as String?,
      zip_code: json['zip_code'] as String?,
      addr1: json['addr1'] as String?,
      addr2: json['addr2'] as String?,
      tel_no: json['tel_no'] as String?,
      mail_addr: json['mail_addr'] as String?,
      soufu_name: json['soufu_name'] as String?,
      soufu_zip_code: json['soufu_zip_code'] as String?,
      soufu_addr1: json['soufu_addr1'] as String?,
      soufu_addr2: json['soufu_addr2'] as String?,
      image_photo: json['image_photo'] as String?,
      filename_photo: json['filename_photo'] as String?,
      filetype_photo: json['filetype_photo'] as String?,
      image_kakunin_omote: json['image_kakunin_omote'] as String?,
      filename_kakunin_omote: json['filename_kakunin_omote'] as String?,
      filetype_kakunin_omote: json['filetype_kakunin_omote'] as String?,
      image_kakunin_ura: json['image_kakunin_ura'] as String?,
      filename_kakunin_ura: json['filename_kakunin_ura'] as String?,
      filetype_kakunin_ura: json['filetype_kakunin_ura'] as String?,
      image_kakunin_atsumi: json['image_kakunin_atsumi'] as String?,
      filename_kakunin_atsumi: json['filename_kakunin_atsumi'] as String?,
      filetype_kakunin_atsumi: json['filetype_kakunin_atsumi'] as String?,
      image_kakunin_rename: json['image_kakunin_rename'] as String?,
      filename_kakunin_rename: json['filename_kakunin_rename'] as String?,
      filetype_kakunin_rename: json['filetype_kakunin_rename'] as String?,
      url: json['url'] as String?,
      irai_data: json['irai_data'] == null
          ? null
          : ReRequest.fromJson(json['irai_data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ReRequestDocImplToJson(_$ReRequestDocImpl instance) =>
    <String, dynamic>{
      'uketsuke_id': instance.uketsuke_id,
      'name': instance.name,
      'name_kana': instance.name_kana,
      'birth_day': instance.birth_day,
      'zip_code': instance.zip_code,
      'addr1': instance.addr1,
      'addr2': instance.addr2,
      'tel_no': instance.tel_no,
      'mail_addr': instance.mail_addr,
      'soufu_name': instance.soufu_name,
      'soufu_zip_code': instance.soufu_zip_code,
      'soufu_addr1': instance.soufu_addr1,
      'soufu_addr2': instance.soufu_addr2,
      'image_photo': instance.image_photo,
      'filename_photo': instance.filename_photo,
      'filetype_photo': instance.filetype_photo,
      'image_kakunin_omote': instance.image_kakunin_omote,
      'filename_kakunin_omote': instance.filename_kakunin_omote,
      'filetype_kakunin_omote': instance.filetype_kakunin_omote,
      'image_kakunin_ura': instance.image_kakunin_ura,
      'filename_kakunin_ura': instance.filename_kakunin_ura,
      'filetype_kakunin_ura': instance.filetype_kakunin_ura,
      'image_kakunin_atsumi': instance.image_kakunin_atsumi,
      'filename_kakunin_atsumi': instance.filename_kakunin_atsumi,
      'filetype_kakunin_atsumi': instance.filetype_kakunin_atsumi,
      'image_kakunin_rename': instance.image_kakunin_rename,
      'filename_kakunin_rename': instance.filename_kakunin_rename,
      'filetype_kakunin_rename': instance.filetype_kakunin_rename,
      'url': instance.url,
      'irai_data': instance.irai_data,
    };
