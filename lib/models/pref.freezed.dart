// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'pref.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Pref _$PrefFromJson(Map<String, dynamic> json) {
  return _Pref.fromJson(json);
}

/// @nodoc
mixin _$Pref {
  String get jimusyo_code => throw _privateConstructorUsedError; // 事務所コード
  int get pref_code => throw _privateConstructorUsedError; // 都道府県コード
  String get pref_name => throw _privateConstructorUsedError; // 都道府県名
  String get jimusyo_name => throw _privateConstructorUsedError; // 事務所名
  String get jimusyo_kana => throw _privateConstructorUsedError; // 事務所名カナ
  String get zip_code => throw _privateConstructorUsedError; // 郵便番号
  String get addr1 => throw _privateConstructorUsedError; // 住所1
  String get addr2 => throw _privateConstructorUsedError; // 住所2
  String get tel_no => throw _privateConstructorUsedError; // 電話番号
  String get fax_no => throw _privateConstructorUsedError; // FAX番号
  String get top_id => throw _privateConstructorUsedError; // 所長ID
  String get jimusyo_code_formal =>
      throw _privateConstructorUsedError; // 事務所コード正式
  String get jimusyo_code_parent =>
      throw _privateConstructorUsedError; // 親事務所コード
  int get kosyu_jimusyo_flag =>
      throw _privateConstructorUsedError; // 講習会実施事務所フラグ
  int get text_sales_flag => throw _privateConstructorUsedError; // テキスト販売事務所フラグ
  String get memo => throw _privateConstructorUsedError;

  /// Serializes this Pref to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Pref
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PrefCopyWith<Pref> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PrefCopyWith<$Res> {
  factory $PrefCopyWith(Pref value, $Res Function(Pref) then) =
      _$PrefCopyWithImpl<$Res, Pref>;
  @useResult
  $Res call(
      {String jimusyo_code,
      int pref_code,
      String pref_name,
      String jimusyo_name,
      String jimusyo_kana,
      String zip_code,
      String addr1,
      String addr2,
      String tel_no,
      String fax_no,
      String top_id,
      String jimusyo_code_formal,
      String jimusyo_code_parent,
      int kosyu_jimusyo_flag,
      int text_sales_flag,
      String memo});
}

/// @nodoc
class _$PrefCopyWithImpl<$Res, $Val extends Pref>
    implements $PrefCopyWith<$Res> {
  _$PrefCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Pref
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? jimusyo_code = null,
    Object? pref_code = null,
    Object? pref_name = null,
    Object? jimusyo_name = null,
    Object? jimusyo_kana = null,
    Object? zip_code = null,
    Object? addr1 = null,
    Object? addr2 = null,
    Object? tel_no = null,
    Object? fax_no = null,
    Object? top_id = null,
    Object? jimusyo_code_formal = null,
    Object? jimusyo_code_parent = null,
    Object? kosyu_jimusyo_flag = null,
    Object? text_sales_flag = null,
    Object? memo = null,
  }) {
    return _then(_value.copyWith(
      jimusyo_code: null == jimusyo_code
          ? _value.jimusyo_code
          : jimusyo_code // ignore: cast_nullable_to_non_nullable
              as String,
      pref_code: null == pref_code
          ? _value.pref_code
          : pref_code // ignore: cast_nullable_to_non_nullable
              as int,
      pref_name: null == pref_name
          ? _value.pref_name
          : pref_name // ignore: cast_nullable_to_non_nullable
              as String,
      jimusyo_name: null == jimusyo_name
          ? _value.jimusyo_name
          : jimusyo_name // ignore: cast_nullable_to_non_nullable
              as String,
      jimusyo_kana: null == jimusyo_kana
          ? _value.jimusyo_kana
          : jimusyo_kana // ignore: cast_nullable_to_non_nullable
              as String,
      zip_code: null == zip_code
          ? _value.zip_code
          : zip_code // ignore: cast_nullable_to_non_nullable
              as String,
      addr1: null == addr1
          ? _value.addr1
          : addr1 // ignore: cast_nullable_to_non_nullable
              as String,
      addr2: null == addr2
          ? _value.addr2
          : addr2 // ignore: cast_nullable_to_non_nullable
              as String,
      tel_no: null == tel_no
          ? _value.tel_no
          : tel_no // ignore: cast_nullable_to_non_nullable
              as String,
      fax_no: null == fax_no
          ? _value.fax_no
          : fax_no // ignore: cast_nullable_to_non_nullable
              as String,
      top_id: null == top_id
          ? _value.top_id
          : top_id // ignore: cast_nullable_to_non_nullable
              as String,
      jimusyo_code_formal: null == jimusyo_code_formal
          ? _value.jimusyo_code_formal
          : jimusyo_code_formal // ignore: cast_nullable_to_non_nullable
              as String,
      jimusyo_code_parent: null == jimusyo_code_parent
          ? _value.jimusyo_code_parent
          : jimusyo_code_parent // ignore: cast_nullable_to_non_nullable
              as String,
      kosyu_jimusyo_flag: null == kosyu_jimusyo_flag
          ? _value.kosyu_jimusyo_flag
          : kosyu_jimusyo_flag // ignore: cast_nullable_to_non_nullable
              as int,
      text_sales_flag: null == text_sales_flag
          ? _value.text_sales_flag
          : text_sales_flag // ignore: cast_nullable_to_non_nullable
              as int,
      memo: null == memo
          ? _value.memo
          : memo // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PrefImplCopyWith<$Res> implements $PrefCopyWith<$Res> {
  factory _$$PrefImplCopyWith(
          _$PrefImpl value, $Res Function(_$PrefImpl) then) =
      __$$PrefImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String jimusyo_code,
      int pref_code,
      String pref_name,
      String jimusyo_name,
      String jimusyo_kana,
      String zip_code,
      String addr1,
      String addr2,
      String tel_no,
      String fax_no,
      String top_id,
      String jimusyo_code_formal,
      String jimusyo_code_parent,
      int kosyu_jimusyo_flag,
      int text_sales_flag,
      String memo});
}

/// @nodoc
class __$$PrefImplCopyWithImpl<$Res>
    extends _$PrefCopyWithImpl<$Res, _$PrefImpl>
    implements _$$PrefImplCopyWith<$Res> {
  __$$PrefImplCopyWithImpl(_$PrefImpl _value, $Res Function(_$PrefImpl) _then)
      : super(_value, _then);

  /// Create a copy of Pref
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? jimusyo_code = null,
    Object? pref_code = null,
    Object? pref_name = null,
    Object? jimusyo_name = null,
    Object? jimusyo_kana = null,
    Object? zip_code = null,
    Object? addr1 = null,
    Object? addr2 = null,
    Object? tel_no = null,
    Object? fax_no = null,
    Object? top_id = null,
    Object? jimusyo_code_formal = null,
    Object? jimusyo_code_parent = null,
    Object? kosyu_jimusyo_flag = null,
    Object? text_sales_flag = null,
    Object? memo = null,
  }) {
    return _then(_$PrefImpl(
      jimusyo_code: null == jimusyo_code
          ? _value.jimusyo_code
          : jimusyo_code // ignore: cast_nullable_to_non_nullable
              as String,
      pref_code: null == pref_code
          ? _value.pref_code
          : pref_code // ignore: cast_nullable_to_non_nullable
              as int,
      pref_name: null == pref_name
          ? _value.pref_name
          : pref_name // ignore: cast_nullable_to_non_nullable
              as String,
      jimusyo_name: null == jimusyo_name
          ? _value.jimusyo_name
          : jimusyo_name // ignore: cast_nullable_to_non_nullable
              as String,
      jimusyo_kana: null == jimusyo_kana
          ? _value.jimusyo_kana
          : jimusyo_kana // ignore: cast_nullable_to_non_nullable
              as String,
      zip_code: null == zip_code
          ? _value.zip_code
          : zip_code // ignore: cast_nullable_to_non_nullable
              as String,
      addr1: null == addr1
          ? _value.addr1
          : addr1 // ignore: cast_nullable_to_non_nullable
              as String,
      addr2: null == addr2
          ? _value.addr2
          : addr2 // ignore: cast_nullable_to_non_nullable
              as String,
      tel_no: null == tel_no
          ? _value.tel_no
          : tel_no // ignore: cast_nullable_to_non_nullable
              as String,
      fax_no: null == fax_no
          ? _value.fax_no
          : fax_no // ignore: cast_nullable_to_non_nullable
              as String,
      top_id: null == top_id
          ? _value.top_id
          : top_id // ignore: cast_nullable_to_non_nullable
              as String,
      jimusyo_code_formal: null == jimusyo_code_formal
          ? _value.jimusyo_code_formal
          : jimusyo_code_formal // ignore: cast_nullable_to_non_nullable
              as String,
      jimusyo_code_parent: null == jimusyo_code_parent
          ? _value.jimusyo_code_parent
          : jimusyo_code_parent // ignore: cast_nullable_to_non_nullable
              as String,
      kosyu_jimusyo_flag: null == kosyu_jimusyo_flag
          ? _value.kosyu_jimusyo_flag
          : kosyu_jimusyo_flag // ignore: cast_nullable_to_non_nullable
              as int,
      text_sales_flag: null == text_sales_flag
          ? _value.text_sales_flag
          : text_sales_flag // ignore: cast_nullable_to_non_nullable
              as int,
      memo: null == memo
          ? _value.memo
          : memo // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PrefImpl implements _Pref {
  const _$PrefImpl(
      {required this.jimusyo_code,
      required this.pref_code,
      required this.pref_name,
      required this.jimusyo_name,
      required this.jimusyo_kana,
      required this.zip_code,
      required this.addr1,
      required this.addr2,
      required this.tel_no,
      required this.fax_no,
      required this.top_id,
      required this.jimusyo_code_formal,
      required this.jimusyo_code_parent,
      required this.kosyu_jimusyo_flag,
      required this.text_sales_flag,
      required this.memo});

  factory _$PrefImpl.fromJson(Map<String, dynamic> json) =>
      _$$PrefImplFromJson(json);

  @override
  final String jimusyo_code;
// 事務所コード
  @override
  final int pref_code;
// 都道府県コード
  @override
  final String pref_name;
// 都道府県名
  @override
  final String jimusyo_name;
// 事務所名
  @override
  final String jimusyo_kana;
// 事務所名カナ
  @override
  final String zip_code;
// 郵便番号
  @override
  final String addr1;
// 住所1
  @override
  final String addr2;
// 住所2
  @override
  final String tel_no;
// 電話番号
  @override
  final String fax_no;
// FAX番号
  @override
  final String top_id;
// 所長ID
  @override
  final String jimusyo_code_formal;
// 事務所コード正式
  @override
  final String jimusyo_code_parent;
// 親事務所コード
  @override
  final int kosyu_jimusyo_flag;
// 講習会実施事務所フラグ
  @override
  final int text_sales_flag;
// テキスト販売事務所フラグ
  @override
  final String memo;

  @override
  String toString() {
    return 'Pref(jimusyo_code: $jimusyo_code, pref_code: $pref_code, pref_name: $pref_name, jimusyo_name: $jimusyo_name, jimusyo_kana: $jimusyo_kana, zip_code: $zip_code, addr1: $addr1, addr2: $addr2, tel_no: $tel_no, fax_no: $fax_no, top_id: $top_id, jimusyo_code_formal: $jimusyo_code_formal, jimusyo_code_parent: $jimusyo_code_parent, kosyu_jimusyo_flag: $kosyu_jimusyo_flag, text_sales_flag: $text_sales_flag, memo: $memo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PrefImpl &&
            (identical(other.jimusyo_code, jimusyo_code) ||
                other.jimusyo_code == jimusyo_code) &&
            (identical(other.pref_code, pref_code) ||
                other.pref_code == pref_code) &&
            (identical(other.pref_name, pref_name) ||
                other.pref_name == pref_name) &&
            (identical(other.jimusyo_name, jimusyo_name) ||
                other.jimusyo_name == jimusyo_name) &&
            (identical(other.jimusyo_kana, jimusyo_kana) ||
                other.jimusyo_kana == jimusyo_kana) &&
            (identical(other.zip_code, zip_code) ||
                other.zip_code == zip_code) &&
            (identical(other.addr1, addr1) || other.addr1 == addr1) &&
            (identical(other.addr2, addr2) || other.addr2 == addr2) &&
            (identical(other.tel_no, tel_no) || other.tel_no == tel_no) &&
            (identical(other.fax_no, fax_no) || other.fax_no == fax_no) &&
            (identical(other.top_id, top_id) || other.top_id == top_id) &&
            (identical(other.jimusyo_code_formal, jimusyo_code_formal) ||
                other.jimusyo_code_formal == jimusyo_code_formal) &&
            (identical(other.jimusyo_code_parent, jimusyo_code_parent) ||
                other.jimusyo_code_parent == jimusyo_code_parent) &&
            (identical(other.kosyu_jimusyo_flag, kosyu_jimusyo_flag) ||
                other.kosyu_jimusyo_flag == kosyu_jimusyo_flag) &&
            (identical(other.text_sales_flag, text_sales_flag) ||
                other.text_sales_flag == text_sales_flag) &&
            (identical(other.memo, memo) || other.memo == memo));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      jimusyo_code,
      pref_code,
      pref_name,
      jimusyo_name,
      jimusyo_kana,
      zip_code,
      addr1,
      addr2,
      tel_no,
      fax_no,
      top_id,
      jimusyo_code_formal,
      jimusyo_code_parent,
      kosyu_jimusyo_flag,
      text_sales_flag,
      memo);

  /// Create a copy of Pref
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PrefImplCopyWith<_$PrefImpl> get copyWith =>
      __$$PrefImplCopyWithImpl<_$PrefImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PrefImplToJson(
      this,
    );
  }
}

abstract class _Pref implements Pref {
  const factory _Pref(
      {required final String jimusyo_code,
      required final int pref_code,
      required final String pref_name,
      required final String jimusyo_name,
      required final String jimusyo_kana,
      required final String zip_code,
      required final String addr1,
      required final String addr2,
      required final String tel_no,
      required final String fax_no,
      required final String top_id,
      required final String jimusyo_code_formal,
      required final String jimusyo_code_parent,
      required final int kosyu_jimusyo_flag,
      required final int text_sales_flag,
      required final String memo}) = _$PrefImpl;

  factory _Pref.fromJson(Map<String, dynamic> json) = _$PrefImpl.fromJson;

  @override
  String get jimusyo_code; // 事務所コード
  @override
  int get pref_code; // 都道府県コード
  @override
  String get pref_name; // 都道府県名
  @override
  String get jimusyo_name; // 事務所名
  @override
  String get jimusyo_kana; // 事務所名カナ
  @override
  String get zip_code; // 郵便番号
  @override
  String get addr1; // 住所1
  @override
  String get addr2; // 住所2
  @override
  String get tel_no; // 電話番号
  @override
  String get fax_no; // FAX番号
  @override
  String get top_id; // 所長ID
  @override
  String get jimusyo_code_formal; // 事務所コード正式
  @override
  String get jimusyo_code_parent; // 親事務所コード
  @override
  int get kosyu_jimusyo_flag; // 講習会実施事務所フラグ
  @override
  int get text_sales_flag; // テキスト販売事務所フラグ
  @override
  String get memo;

  /// Create a copy of Pref
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PrefImplCopyWith<_$PrefImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
