// ファイル名: main.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'course_guide.freezed.dart';
part 'course_guide.g.dart';

@freezed
class CourseGuide with _$CourseGuide {
  const factory CourseGuide({
    required String course_name, // 講習コード
    required String hold_licence,
  }) = _CourseGuide;

  factory CourseGuide.fromJson(Map<String, Object?> json) =>
      _$CourseGuideFromJson(json);
}
