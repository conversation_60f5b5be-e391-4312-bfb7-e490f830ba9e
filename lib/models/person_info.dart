// This file is "main.dart"
import 'package:freezed_annotation/freezed_annotation.dart';

part 'person_info.freezed.dart';
part 'person_info.g.dart';

@freezed
class PersonInfo with _$PersonInfo {
  const factory PersonInfo({
    required String id,
    required String name,
    required String email,
    required String uketsukeId,
    required String status,
  }) = _PersonInfo;

  factory PersonInfo.fromJson(Map<String, Object?> json) =>
      _$PersonInfoFromJson(json);
}
