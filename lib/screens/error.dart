import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:koushuu_system/widgets/core_button.dart';
import 'package:koushuu_system/widgets/default_text.dart';
import 'package:koushuu_system/widgets/footer_box.dart';
import 'package:koushuu_system/widgets/bottom_bar.dart';
import 'package:koushuu_system/widgets/top_bar.dart';

class ErrorPage extends StatefulWidget {
  const ErrorPage(
      {super.key,
      this.title = "エラーが発生しました。",
      this.msg = "エラーが発生しました。しばらくしたら再度お試しください。"});

  final String title;
  final String msg;

  @override
  State<ErrorPage> createState() => _ErrorPageState();
}

class _ErrorPageState extends State<ErrorPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: SingleChildScrollView(
          controller: _scrollController,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Row(
                children: [
                  TopBar(
                    title: widget.title,
                  ),
                ],
              ),

              const SizedBox(height: 30),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  DefaultText(
                    txt: widget.msg,
                  ),
                ],
              ),
              const SizedBox(height: 30),
              // Buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CoreButton(
                    onPressed: () {
                      GoRouter.of(context).go('/');
                    },
                    title: 'TOPへ',
                  ),
                ],
              ),
              const SizedBox(height: 30),
              const Row(
                children: [
                  FooterBox(),
                ],
              ),
            ],
          ),
        ),
        bottomNavigationBar: const BottomBar(title: ''));
  }
}
