import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:go_router/go_router.dart';
import 'package:koushuu_system/widgets/bottom_bar.dart';
import 'package:koushuu_system/widgets/core_button.dart';
import 'package:koushuu_system/widgets/top_bar.dart';
import 'package:koushuu_system/widgets/up_fab.dart';

class ChangePage extends StatefulWidget {
  const ChangePage({super.key, required this.title});

  final String title;

  @override
  State<ChangePage> createState() => _ChangePageState();
}

class _ChangePageState extends State<ChangePage> {
  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: SingleChildScrollView(
          controller: _scrollController,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              const Row(
                children: [
                  TopBar(
                    title: '再交付等のご案内',
                  ),
                ],
              ),
              Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const HtmlWidget(
                            '''
  <p>
    再交付申請には下記の書類等が必要になります、事前にご一読いただき、ご準備ください。
  </p>
  <p>
  <h3>確認1</h3>
    スマートフォン等のカメラで撮影してください（JPEGデータ）<br/>
    <ol>
      <li>証明写真<br/>（正面、脱帽、背景無地のもの。サングラスやマスク着用のないもの）<br/></li>

      <img src="asset:assets/face.png"/>
      <li>本人確認証明（下記のいずれか1種類）
        <ul>
          <li>転免許証</li>
          <li>マイナンバーカード</li>
          <li>在留カード</li>
        </ul>
      </li>
      表と裏と厚みを撮影してください。<br/>（マイナンバーカードは表と厚みのみとしてください）
      <img src="asset:assets/id.png"/><br/>（表）の写真と同じ顔写真であることが分かるように撮影してください。
    </ol>

  </p>
  <p>
  <h3>確認2</h3>
    氏名の変更がある場合
      <li>本人確認証明（下記のいずれか1種類）
        <ul>
          <li>戸籍抄本（原本）※コピー不可<br/>（変更前後の氏名が記載されているもの）
          <center><img src="asset:assets/postal.png" style="width:200px;" /></center><br/>再交付等申請の後、送付をお願いします。</li>
        </ul>
      </li>
  </p>
  <p>
  <h3>確認3</h3>
    再交付等手数料など<br/>
    <ul>
      <li>統合修了証カード　1枚　2，200円（消費税込み）</li>
      <li>修了証送付料金　　1通　簡易書留　434円<br/>（請求書をご確認のうえ、お振込みをお願いします）</li>
    </ul>
    <small><i>※「修了証送付料金」は修了証を申請者様宛へ送付する送料です。</i></small>
    <center><img src="asset:assets/atm.png" style="width:150px;"/></center>
  </p>
  <p>
  <h3>確認4</h3>
    修了証が（紛失などしていない）現存する場合<br/>
    <ul>
      <li>古い修了証は回収となります。</li>
      <li>新しい修了証を受け取り後、古い修了証を再交付申請先に送付ください。</li>
    </ul>
  </p>
  ''',
                          ),
                          const SizedBox(height: 20),
                          Center(
                            child: CoreButton(
                              onPressed: () {
                                GoRouter.of(context).go('/changedesc');
                              },
                              title: '次へ',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(
                height: 20,
              ),
            ],
          ),
        ),
        floatingActionButton: UpFabButton(
          scrollViewController: _scrollController,
        ),
        bottomNavigationBar: const BottomBar(title: ''));
  }
}
