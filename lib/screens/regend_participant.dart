import 'package:flutter/material.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:go_router/go_router.dart';
import 'package:koushuu_system/widgets/core_button.dart';
import 'package:koushuu_system/widgets/default_text.dart';
import 'package:koushuu_system/widgets/footer_box.dart';
import 'package:koushuu_system/widgets/bottom_bar.dart';
import 'package:koushuu_system/widgets/top_bar.dart';

class RegEndParticipantPage extends StatefulWidget {
  const RegEndParticipantPage(
      {super.key, this.title = "", required this.showParticipantsButton});

  final String title;
  final int showParticipantsButton;

  @override
  State<RegEndParticipantPage> createState() => _RegEndParticipantPageState();
}

class _RegEndParticipantPageState extends State<RegEndParticipantPage> {
  final ScrollController _scrollController = ScrollController();

  String isList = "0";

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    SessionManager().get('isList').then((val) {
      isList = val.toString();
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: SingleChildScrollView(
          controller: _scrollController,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Row(
                children: [
                  TopBar(
                    title: widget.title,
                  ),
                ],
              ),

              const SizedBox(height: 30),

              // Requirements Section
              const DefaultText(
                txt: 'ご利用ありがとうございます。',
              ),
              const DefaultText(
                txt: '受講者情報の入力を受け付けました。',
              ),
              const SizedBox(height: 40),
              const DefaultText(
                txt: '公益社団法人　ボイラ・クレーン安全協会',
              ),

              const SizedBox(height: 20),
              // Buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  isList == "1"
                      ? CoreButton(
                          onPressed: () {
                            GoRouter.of(context).go('/participants');
                          },
                          title: '受講者一覧へ',
                        )
                      : const SizedBox(
                          width: 0,
                        ),
                  const SizedBox(
                    width: 20,
                  ),
                  CoreButton(
                    onPressed: () {
                      GoRouter.of(context).go('/');
                    },
                    title: 'TOPへ',
                  ),
                ],
              ),
              const SizedBox(height: 30),
              const Row(
                children: [
                  FooterBox(),
                ],
              ),
            ],
          ),
        ),
        bottomNavigationBar: const BottomBar(title: ''));
  }
}
