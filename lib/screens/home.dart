import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:koushuu_system/widgets/footer_box.dart';
import 'package:koushuu_system/widgets/bottom_bar.dart';
import 'package:koushuu_system/widgets/custom_card.dart';
import 'package:koushuu_system/widgets/top_bar.dart';
import 'package:koushuu_system/widgets/up_fab.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key, required this.title});

  final String title;

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final List<String> imgList = [
    'assets/topslide_01.jpg',
    'assets/topslide_03.jpg',
  ];

  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: SingleChildScrollView(
          controller: _scrollController,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              const Row(
                children: [
                  TopBar(
                    title: '',
                  ),
                ],
              ),
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        GoRouter.of(context).go('/search');
                      },
                      child: CarouselSlider(
                        options: CarouselOptions(
                          aspectRatio: 1024 / 416,
                          viewportFraction: 1,
                          autoPlay: true,
                          pageSnapping: false,
                        ),
                        items: imgList
                            .map(
                              (item) => Image.asset(item),
                              //Image.network(item, fit: BoxFit.contain),
                            )
                            .toList(),
                      ),
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  Expanded(
                    child: CustomCard(
                      icon: Icons.calendar_month,
                      text: '講習の予約・日程',
                      backgroundColor: Colors.cyan.shade400,
                      onTap: () {
                        GoRouter.of(context).go('/search');
                      },
                    ),
                  ),
                  Expanded(
                    child: CustomCard(
                      icon: Icons.apartment,
                      text: '再発行',
                      backgroundColor: Colors.brown.shade400,
                      onTap: () {
                        GoRouter.of(context).go('/change');
                      },
                    ),
                  ),
                ],
              ),
              const Row(
                children: [
                  FooterBox(),
                ],
              ),
            ],
          ),
        ),
        floatingActionButton: UpFabButton(
          scrollViewController: _scrollController,
        ),

        // ScrollingFabAnimated(
        //   icon: const Icon(
        //     Icons.upgrade_sharp,
        //     color: Colors.white,
        //   ),
        //   text: const Text(
        //     'トップ',
        //     style: TextStyle(color: Colors.white, fontSize: 16.0),
        //   ),
        //   onPress: () {
        //     _scrollController.animateTo(
        //         _scrollController.position.minScrollExtent,
        //         duration: const Duration(milliseconds: 200),
        //         curve: Curves.ease);
        //   },
        //   color: Theme.of(context).colorScheme.inversePrimary,
        //   scrollController: _scrollController,
        //   animateIcon: true,
        //   inverted: true,
        //   radius: 10.0,
        // ),
        bottomNavigationBar: const BottomBar(title: ''));
  }
}
