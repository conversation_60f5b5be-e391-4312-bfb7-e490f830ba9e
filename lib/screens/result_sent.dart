import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:koushuu_system/widgets/core_button.dart';
import 'package:koushuu_system/widgets/default_text.dart';
import 'package:koushuu_system/widgets/footer_box.dart';
import 'package:koushuu_system/widgets/bottom_bar.dart';
import 'package:koushuu_system/widgets/top_bar.dart';

class SearchResultSentPage extends StatefulWidget {
  const SearchResultSentPage({super.key, this.title = "おメール送信しました。"});

  final String title;

  @override
  State<SearchResultSentPage> createState() => _SearchResultSentPageState();
}

class _SearchResultSentPageState extends State<SearchResultSentPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: SingleChildScrollView(
          controller: _scrollController,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Row(
                children: [
                  TopBar(
                    title: widget.title,
                  ),
                ],
              ),

              const SizedBox(height: 30),

              // Requirements Section
              const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  DefaultText(
                    txt: 'ご利用ありがとうございます。',
                  ),
                ],
              ),
              const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  DefaultText(
                    txt: 'メールのご確認をお願い致します。',
                  ),
                ],
              ),
              const SizedBox(height: 30),
              // Buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CoreButton(
                    onPressed: () {
                      GoRouter.of(context).go('/');
                    },
                    title: 'TOPへ',
                  ),
                  // const SizedBox(
                  //   width: 20,
                  // ),
                  // CoreButton(
                  //   onPressed: () {
                  //     GoRouter.of(context).go('/rparticipant');
                  //   },
                  //   title: '＜仮テスト用＞',
                  // ),
                ],
              ),
              const SizedBox(height: 30),
              const Row(
                children: [
                  FooterBox(),
                ],
              ),
            ],
          ),
        ),
        bottomNavigationBar: const BottomBar(title: ''));
  }
}
