import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:koushuu_system/modals/check_participant.dart';
import 'package:koushuu_system/models/participant.dart';
import 'package:koushuu_system/models/person_info.dart';
import 'package:koushuu_system/providers/appstate_provider.dart';
import 'package:koushuu_system/repositories/participant_repository.dart';
import 'package:koushuu_system/repositories/request_repository.dart';
import 'package:koushuu_system/widgets/bottom_bar.dart';
import 'package:koushuu_system/widgets/core_button.dart';
import 'package:koushuu_system/widgets/default_text.dart';
import 'package:koushuu_system/widgets/top_bar.dart';
import 'package:koushuu_system/widgets/up_fab.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:koushuu_system/core/constants.dart' as constants;

class ParticipantsPage extends ConsumerStatefulWidget {
  ParticipantsPage({
    super.key,
    required this.title,
  });

  final String title;
  final int personCnt = 0;
  String applicantName = "";

  @override
  ConsumerState<ParticipantsPage> createState() => _ParticipantsPageState();
}

class _ParticipantsPageState extends ConsumerState<ParticipantsPage> {
  String applicationId = "";
  String login_email = "";
  String isList = "0";

  @override
  void initState() {
    // applicationId = ref.read(appStateProvider).applicationId!;
    SessionManager().get('application_id').then((a) {
      applicationId = a.toString();
    });

    SessionManager().get('login_email').then((a) {
      login_email = a.toString();
    });

    SessionManager().get('isList').then((val) {
      isList = val.toString();
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // if (isList == "0") {
    //   GoRouter.of(context).go('/');
    // }

    late int cnt = 0;
    final ScrollController scrollController = ScrollController();
    final formKey = GlobalKey<FormBuilderState>();

    final fetchAsync =
        ref.watch(getParticipantsProvider(applicationId, login_email));

    Widget _buildTextField(
      int index,
      String name,
      String label,
      String value,
      String? Function(String?)? validator,
      String? hintText,
    ) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 10.0),
        child: FormBuilderTextField(
          name: name,
          // readOnly: true,
          initialValue: value,
          //controller: nameControllers[index],
          decoration: InputDecoration(
            filled: true,
            fillColor: Colors.amber[50],
            labelText: label,
            hintText: hintText,
            floatingLabelBehavior: FloatingLabelBehavior.always,
            hintStyle: const TextStyle(color: Colors.grey),
            border: const OutlineInputBorder(),
          ),
          validator: validator,
        ),
      );
    }

    Widget _buildParticipantInfo(
      index,
      BuildContext context,
      PersonInfo item,
      WidgetRef ref,
    ) {
      // String applicationId = ref.read(appStateProvider).applicationId!;
      // String applicationId =  await SessionManager().get('application_id');
      // applicationId = applicationId.isEmpty ? "" : applicationId;

      return Expanded(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            children: [
              _buildTextField(
                index,
                "name_$index",
                "氏名",
                item.name,
                FormBuilderValidators.compose([
                  FormBuilderValidators.required(),
                ]),
                "安全　太郎（姓名の間は空白）",
              ),
              _buildTextField(
                index,
                "email_$index",
                "メールアドレス",
                item.email,
                FormBuilderValidators.compose([
                  FormBuilderValidators.required(),
                  FormBuilderValidators.email(),
                ]),
                "anzen@◯△x.co.jp",
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CoreButton(
                      onPressed: () async {
                        formKey.currentState?.save();

                        final frmState = formKey.currentState;
                        final eml = frmState?.value["email_$index"];
                        final nme = frmState?.value["name_$index"];
                        if (eml == "" || nme == "") {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text("名称、メールアドレスが必須です。")),
                          );
                          return;
                        }

                        final resDlg = await showDialog(
                          context: context,
                          builder: (BuildContext context) {
                            return AlertDialog(
                              title: const Text(
                                '確認',
                                style: TextStyle(color: Colors.black),
                              ),
                              content: Text(
                                  textAlign: TextAlign.center,
                                  style: const TextStyle(
                                    color: Colors.black,
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  '受講者情報の登録を依頼しますがよろしいですか？\n受講者名：$nme\n\n「はい」で受講者に登録依頼のURLがメールで送信されます。'),
                              actions: <Widget>[
                                Center(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      CoreButton(
                                        onPressed: () {
                                          Navigator.of(context)
                                              .pop(true); // Return true
                                        },
                                        title: 'はい',
                                      ),
                                      const SizedBox(
                                        height: 20,
                                      ),
                                      CoreButton(
                                        onPressed: () {
                                          Navigator.of(context)
                                              .pop(false); // Return false
                                        },
                                        title: 'いいえ',
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            );
                          },
                        );

                        if (resDlg == null || !resDlg) {
                          return;
                        }

                        // formKey.currentState?.save();

                        // final frmState = formKey.currentState;
                        // final eml = frmState?.value["email_$index"];
                        // final nme = frmState?.value["name_$index"];
                        // if (eml == "" || nme == "") {
                        //   ScaffoldMessenger.of(context).showSnackBar(
                        //     const SnackBar(content: Text("名称、メールアドレスが必須です。")),
                        //   );
                        //   return;
                        // }
                        //final loginName = ref.read(appStateProvider).loginName;
                        Map<String, dynamic> formValues = {
                          "email": eml,
                          "name": nme,
                          "applicant_name": widget.applicantName,
                          "application_id": applicationId,
                          "uketsuke_id": item.uketsukeId,
                        };

                        context.loaderOverlay.show();
                        try {
                          final res = await ref
                              .read(participantRepositoryProvider)
                              .setParticipant(formValues);

                          context.loaderOverlay.hide();

                          if (res["status"] == constants.API_RES_SUCCESS) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                  content: Text("受講者情報登録依頼のメール送信しました！")),
                            );

                            // Reload the participants list
                            await ref.refresh(getParticipantsProvider(
                                    applicationId, login_email)
                                .future);
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                  content: Text("受講者情報登録依頼のメール送信出来ませんでした！")),
                            );
                          }
                        } catch (e) {
                          context.loaderOverlay.hide();
                        }
                      },
                      bgColor: Colors.red,
                      title: "受講者が入力"),
                  Text(
                    item.status == "0" ? "未入力" : "入力完了",
                    style: item.status == "0"
                        ? const TextStyle(fontWeight: FontWeight.bold)
                        : const TextStyle(
                            fontWeight: FontWeight.bold,
                            backgroundColor: Colors.blue,
                            color: Colors.white),
                  ),
                  CoreButton(
                      onPressed: () async {
                        // GoRouter.of(context).go("/rparticipant");

                        context.loaderOverlay.show();
                        final res = await ref
                            .read(participantRepositoryProvider)
                            .getParticipant(item.id, ""); // item.uketsukeId);

                        context.loaderOverlay.hide();
                        if (res["status"] == constants.API_RES_SUCCESS) {
                          Participant p =
                              Participant.fromJson(res["responsedata"]);

                          // (
                          //   id: member_id,
                          //   application_id: application_id,
                          //   uketsuke_id: uid ?? '',
                          //   kosyu_number: '',
                          //   kosyu_code: '',
                          //   kosyu_date: '',
                          //   course_code: '',
                          //   jimusyo_code: "",
                          //   kaijyo_code: '',
                          //   applicant_number: pid ?? "",
                          //   name1: name1Value.toString(),
                          //   name2: name2Value.toString(),
                          //   name_kana: furiganaValue,
                          //   old_or_common_name_type: optValue,
                          //   reason: reasonTitle,
                          //   name3: '',
                          //   birth_day: birthdayValue,
                          //   zip_code: postalValue,
                          //   addr1: addr1Value,
                          //   addr2: addr2Value,
                          //   tel_no: tel1Value,
                          //   mail_addr: emailValue,
                          //   opt_company: optCompanyValue,
                          //   declaration_party_name: declaration_party_name,
                          //   declaration_date: declaration_date,
                          //   soufu_kubun: optSofuValue,
                          //   url: '',
                          //   image_photo: base64photo1,
                          //   filename_photo: base64photo1Name,
                          //   filetype_photo: base64photo1Type,
                          //   image_kakunin_omote: base64photo2,
                          //   filename_kakunin_omote: base64photo2Name,
                          //   filetype_kakunin_omote: base64photo2Type,
                          //   image_kakunin_ura: base64photo3,
                          //   filename_kakunin_ura: base64photo3Name,
                          //   filetype_kakunin_ura: base64photo3Type,
                          //   image_kakunin_atsumi: base64photo4,
                          //   filename_kakunin_atsumi: base64photo4Name,
                          //   filetype_kakunin_atsumi: base64photo4Type,
                          //   image_license1: base64photo5,
                          //   filename_license1: base64photo5Name,
                          //   filetype_license1: base64photo5Type,
                          //   image_license2: base64photo6,
                          //   filename_license2: base64photo6Name,
                          //   filetype_license2: base64photo6Type,
                          //   image_license3: base64photo7,
                          //   filename_license3: base64photo7Name,
                          //   filetype_license3: base64photo7Type,
                          //   unacquired_kosyu_code: unacquired_kosyu_code,
                          //   sinkoku_jimusyo_code: sinkoku_jimusyo_code,
                          //   sinkoku_kosyu_kubun: sinkoku_kosyu_kubun,
                          //   sinkoku_kosyu_date: sinkoku_kosyu_date,
                          //   sinkoku_kuwari_kubun: sinkoku_kuwari_kubun,
                          //   unacquired_kosyu_title: t_unacquired_kosyu_title,
                          //   sinkoku_jimusyo_title: t_sinkoku_jimusyo_title,
                          //   sinkoku_kosyu_kubun_title:
                          //       t_sinkoku_kosyu_kubun_title,
                          //   sinkoku_kuwari_kubun_title:
                          //       t_sinkoku_kuwari_kubun_title,
                          // );

                          showModalBottomSheet(
                            useSafeArea: true,
                            isScrollControlled: true,
                            context: context,
                            builder: (ctx) => CheckParticipantPage(
                              title: '受講者情報（確認）',
                              p: p,
                              flgView: 1,
                            ),
                          );
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text("登録された情報ありません！"),
                            ),
                          );
                        }
                      },
                      title: "確認"),
                ],
              ),
              const SizedBox(
                height: 10,
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: CoreButton(
                        onPressed: () async {
                          context.loaderOverlay.show();

                          await SessionManager().set('member_id', item.id);

                          context.loaderOverlay.hide();

                          GoRouter.of(context).go('/rparticipant');

                          // final res = await ref
                          //     .read(participantRepositoryProvider)
                          //     .getParticipant(item.id, "");

                          //context.loaderOverlay.hide();
                          //if (res["status"] == constants.API_RES_SUCCESS) {
                          // final Participant p =
                          //     Participant.fromJson(res["responsedata"]);
                          //GoRouter.of(context).go('/rparticipant', extra: p);
                          // } else {}
                        },
                        title: "続けて個人情報登録を行う"),
                  ),
                ],
              ),
              const SizedBox(
                height: 30,
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
        body: FormBuilder(
          key: formKey,
          child: SingleChildScrollView(
            controller: scrollController,
            child: fetchAsync.when(
              data: (dt) {
                if (dt["status"] == constants.API_RES_FAILED) {
                  return const Center(
                    child: DefaultText(
                      txt: 'データ取得出来ませんでした。',
                    ),
                  );
                }
                if (dt["data"] == null) {
                  return const Center(
                    child: DefaultText(
                      txt: 'データがありません',
                    ),
                  );
                }
                final peopleCnt = dt["data"]["people_cnt"];
                widget.applicantName = dt["data"]["applicant_name"];
                List<Map<String, dynamic>> lst =
                    List<Map<String, dynamic>>.from(dt["data"]["list"]);
                var cntDone = 0;
                var cntWait = 0;

                bool allStatus = true;
                for (var el in lst) {
                  if (el["status"] == 0) {
                    cntWait++;
                    allStatus = false;
                    //break;
                  } else {
                    cntDone++;
                  }
                }
                //final remains = peopleCnt - lst.length;
                return Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                    const Row(
                      children: [
                        TopBar(
                          title: '受講者一覧',
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 20.0, right: 20.0),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              DefaultText(
                                txt: '【登録人数$peopleCnt人】',
                              ),
                              const DefaultText(
                                txt: '続けて個人情報登録も可能',
                                fontSize: 12,
                              ),
                            ],
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              DefaultText(
                                txt: '未完了：$cntWait人  入力完了：$cntDone人',
                                fontSize: 14,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    ...lst.map((obj) => Row(
                          children: [
                            _buildParticipantInfo(
                              cnt++,
                              context,
                              PersonInfo(
                                id: obj["id"].toString(),
                                //name: (obj["name1"] + " " + obj["name2"])
                                name: obj["name1"].toString().trim(),
                                email: obj["mail_addr"].toString().trim(),
                                uketsukeId: obj["uketsuke_id"],
                                status: obj["status"].toString(),
                              ),
                              ref,
                            ),
                          ],
                        )),
                    const SizedBox(
                      height: 10,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 20.0, right: 20.0),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CoreButton(
                            onPressed: () {
                              context.loaderOverlay.show();
                              ref.refresh(getParticipantsProvider(
                                      applicationId, login_email)
                                  .future);

                              context.loaderOverlay.hide();
                            },
                            bgColor: Colors.red,
                            title: "更新",
                          ),
                          const SizedBox(
                            width: 20,
                          ),
                          allStatus == true
                              ? CoreButton(
                                  onPressed: () async {
                                    final resDlg = await showDialog(
                                      context: context,
                                      builder: (BuildContext context) {
                                        return AlertDialog(
                                          title: const Text(
                                            '確認',
                                            style:
                                                TextStyle(color: Colors.black),
                                          ),
                                          content: const Text(
                                              textAlign: TextAlign.center,
                                              style: TextStyle(
                                                color: Colors.black,
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                              ),
                                              '入力が完了していない受講者がいます。\n全て完了にしてよろしいですか？\n※完了した場合入力することが出来なくなります。'),
                                          actions: <Widget>[
                                            Center(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  CoreButton(
                                                    onPressed: () {
                                                      Navigator.of(context).pop(
                                                          true); // Return true
                                                    },
                                                    title: 'はい',
                                                  ),
                                                  const SizedBox(
                                                    height: 20,
                                                  ),
                                                  CoreButton(
                                                    onPressed: () {
                                                      Navigator.of(context).pop(
                                                          false); // Return false
                                                    },
                                                    title: 'いいえ',
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        );
                                      },
                                    );

                                    if (resDlg == null || !resDlg) {
                                      return;
                                    }
                                    context.loaderOverlay.show();

                                    final res = await ref
                                        .read(requestRepositoryProvider)
                                        .closeRequest(applicationId);

                                    context.loaderOverlay.hide();
                                    if (res["status"] ==
                                        constants.API_RES_SUCCESS) {
                                      GoRouter.of(context).go("/eparticipant",
                                          extra: {"showParticipantsButton": 0});
                                    } else {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        const SnackBar(
                                          content: Text(
                                              "全ての入力が完了していない為、全て完了出来ませんでした。"),
                                        ),
                                      );
                                    }
                                  },
                                  bgColor: Colors.red,
                                  title: "受講者登録　全て完了",
                                )
                              : const SizedBox(
                                  width: 0,
                                  height: 0,
                                ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 40,
                    ),
                  ],
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (e, _) => Text(e.toString()),
            ),
          ),
        ),
        floatingActionButton: UpFabButton(
          scrollViewController: scrollController,
        ),
        bottomNavigationBar: const BottomBar(title: ''));
  }
}
