import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:go_router/go_router.dart';
import 'package:koushuu_system/providers/appstate_provider.dart';
import 'package:koushuu_system/repositories/user_repository.dart';
import 'package:koushuu_system/widgets/bottom_bar.dart';
import 'package:koushuu_system/widgets/core_button.dart';
import 'package:koushuu_system/widgets/default_text.dart';
import 'package:koushuu_system/widgets/top_bar.dart';
import 'package:koushuu_system/widgets/up_fab.dart';
import 'package:koushuu_system/core/constants.dart' as constants;

class RegisterPage extends ConsumerWidget {
  RegisterPage({
    super.key,
    this.title,
    required this.email,
    required this.company,
    required this.id,
    this.memname,
    this.coursename,
    this.companyname,
  });

  final String? title;
  final String email;
  final String id;
  final String company;
  final String? memname;
  final String? coursename;
  final String? companyname;
  final _formKey = GlobalKey<FormBuilderState>();
  final ScrollController _scrollController = ScrollController();
  final TextEditingController passController = TextEditingController();
  final TextEditingController repassController = TextEditingController();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the AppState
    final appState = ref.watch(appStateProvider);

    return Scaffold(
        body: FormBuilder(
          key: _formKey,
          child: SingleChildScrollView(
            controller: _scrollController,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Row(
                  children: [
                    TopBar(
                      title: title ?? '申込者登録　パスワード設定',
                    ),
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
                Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Row(children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Form Section
                          id != ""
                              ? DefaultText(txt: '申込者ID：$id')
                              : Container(),
                          company != ""
                              ? DefaultText(txt: '申込者名：$company')
                              : Container(),
                          memname != ""
                              ? DefaultText(txt: '受講者名：$memname 様')
                              : Container(),
                          const SizedBox(height: 10),
                          coursename != ""
                              ? DefaultText(
                                  txt:
                                      '$companyname様より$coursenameの受講者情報の登録依頼が行われました。',
                                  fontSize: 14,
                                )
                              : Container(),
                          coursename != ""
                              ? const DefaultText(
                                  txt: '始めにパスワードの設定を行ってください。',
                                  fontSize: 14,
                                )
                              : Container(),
                          _buildTextField(
                            "pass",
                            'パスワード',
                            passController,
                            FormBuilderValidators.compose([
                              FormBuilderValidators.required(),
                              FormBuilderValidators.password(
                                  errorText:
                                      "英大文字・小文字・数字・記号を1つ以上含めて、8文字以上にしてください"),
                            ]),
                            true,
                            false,
                          ),
                          _buildTextField(
                            "repass",
                            '再パスワード',
                            repassController,
                            FormBuilderValidators.compose([
                              FormBuilderValidators.required(),
                              FormBuilderValidators.password(),
                              (value) {
                                if (value != passController.text) {
                                  return 'パスワードが一致していません。';
                                }
                                return null;
                              },
                            ]),
                            true,
                            false,
                          ),
                          const Text(
                              "パスワードは8文字以上で、英大文字・小文字・数字・記号を1つ以上含めてください。"),
                        ],
                      ),
                    ),
                  ]),
                ),
                const SizedBox(
                  height: 20,
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CoreButton(
                        onPressed: () {
                          GoRouter.of(context).go('/');
                        },
                        bgColor: Colors.red,
                        fgColor: Colors.white,
                        title: 'キャンセル'),
                    const SizedBox(
                      width: 20,
                    ),
                    CoreButton(
                        onPressed: () async {
                          // Form is valid
                          if (_formKey.currentState?.validate() ?? false) {
                            final aId =
                                await SessionManager().get('application_id');

                            final res = await ref
                                .read(userRepositoryProvider)
                                .setPassword(
                                    email, passController.text, aId.toString());
                            if (res.data["status"] ==
                                constants.API_RES_SUCCESS) {
                              ref
                                  .read(appStateProvider.notifier)
                                  .updateApplicationId(id);

                              // print("Application id: $id");

                              GoRouter.of(context).go('/login', extra: {
                                "mid": id,
                                "mname": company,
                                "email": email,
                              });
                            } else {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                    content: Text("パスワード設定出来ませんでした！")),
                              );
                            }
                          }
                        },
                        bgColor: Colors.red,
                        fgColor: Colors.white,
                        title: '登録'),
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
              ],
            ),
          ),
        ),
        floatingActionButton: UpFabButton(
          scrollViewController: _scrollController,
        ),
        bottomNavigationBar: const BottomBar(title: ''));
  }
}

Widget _buildTextField(
  String name,
  String label,
  TextEditingController controller,
  String? Function(String?)? validator,
  bool isSecure,
  bool readonly,
) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 5),
    child: FormBuilderTextField(
      name: name,
      enabled: !readonly,
      obscureText: isSecure,
      controller: controller,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
      ),
    ),
  );
}
