import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:form_builder_image_picker/form_builder_image_picker.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:koushuu_system/core/utils.dart';
import 'package:koushuu_system/modals/check_participant.dart';
import 'package:koushuu_system/models/participant.dart';
import 'package:koushuu_system/repositories/address_repository.dart';
import 'package:koushuu_system/repositories/jimusho_repository.dart';
import 'package:koushuu_system/repositories/participant_repository.dart';
import 'package:koushuu_system/widgets/bottom_bar.dart';
import 'package:koushuu_system/widgets/core_button.dart';
import 'package:koushuu_system/widgets/custom_file_field.dart';
import 'package:koushuu_system/widgets/default_text.dart';
import 'package:koushuu_system/widgets/error_info.dart';
import 'package:koushuu_system/widgets/field_title.dart';
import 'package:koushuu_system/widgets/top_bar.dart';
import 'package:koushuu_system/widgets/up_fab.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:koushuu_system/core/constants.dart' as constants;
import 'package:form_builder_file_picker/form_builder_file_picker.dart';
import 'package:mime/mime.dart';

final optValueProvider = StateProvider<String>((ref) {
  return "1";
});

final optCompanyValueProvider = StateProvider<String>((ref) {
  return "0";
});

final optSofuValueProvider = StateProvider<String>((ref) {
  return "1";
});

final dteValueProvider = StateProvider<String>((ref) {
  return "";
});

final jimushoValueProvider = StateProvider<String?>((ref) => null);
final kubunValueProvider = StateProvider<String?>((ref) => null);
final koushuuValueProvider = StateProvider<String?>((ref) => null);
final koushuuDateValueProvider = StateProvider<String?>((ref) => null);
final koushuuKuwariValueProvider = StateProvider<String?>((ref) => null);

// Add a new provider to track if data has been initialized
final dataInitializedProvider = StateProvider<bool>((ref) => false);

// ignore: must_be_immutable
class RegParticipantPage extends ConsumerStatefulWidget {
  RegParticipantPage({
    super.key,
    required this.title,
    this.p,
    this.flgOut = 0,
  });

  final String title;
  final int flgOut;
  Participant? p;

  @override
  ConsumerState<RegParticipantPage> createState() => _RegParticipantPageState();
}

class _RegParticipantPageState extends ConsumerState<RegParticipantPage> {
  final _formKey = GlobalKey<FormBuilderState>();
  final ScrollController _scrollController = ScrollController();

  int personCnt = 0;
  String currentOption = "";

  String base64photo1 = "";
  String base64photo1Name = "";
  String base64photo1Type = "";
  String base64photo2 = "";
  String base64photo2Name = "";
  String base64photo2Type = "";
  String base64photo3 = "";
  String base64photo3Name = "";
  String base64photo3Type = "";
  String base64photo4 = "";
  String base64photo4Name = "";
  String base64photo4Type = "";
  String base64photo5 = "";
  String base64photo5Name = "";
  String base64photo5Type = "";
  String base64photo6 = "";
  String base64photo6Name = "";
  String base64photo6Type = "";
  String base64photo7 = "";
  String base64photo7Name = "";
  String base64photo7Type = "";

  String application_id = "0";
  String login_name = "";
  String login_email = "";
  String member_id = "0";
  String uketsuke_id = "0";

  @override
  void initState() {
    SessionManager().get('application_id').then((val) {
      application_id = val.toString();
    });
    SessionManager().get('login_name').then((val) {
      login_name = val.toString();
    });
    SessionManager().get('login_email').then((val) {
      login_email = val.toString();
    });
    SessionManager().get('member_id').then((val) {
      member_id = val.toString();
    });
    SessionManager().get('uketsuke_id').then((val) {
      uketsuke_id = val != null ? val.toString() : "";
    });

    widget.p ??= const Participant(
      id: 0,
      application_id: 0,
      uketsuke_id: "",
      kosyu_number: "",
      kosyu_code: 0,
      kosyu_date: "",
      course_code: 0,
      jimusyo_code: 0,
      kaijyo_code: 0,
      applicant_number: "",
      uketsuke_date: "",
      applicant_code: 0,
      name1: "",
      name2: "",
      name_kana: "",
      old_or_common_name_type: 1,
      reason: "",
      name3: "",
      birth_day: "",
      zip_code: "",
      addr1: "",
      addr2: "",
      tel_no: "",
      mail_addr: "",
      opt_company: 0,
      unacquired_kosyu_code: "0",
      declaration_party_name: "",
      declaration_date: "",
      soufu_kubun: 0,
      url: "",
      image_photo: "",
      filename_photo: "",
      filetype_photo: "",
      image_kakunin_omote: "",
      filename_kakunin_omote: "",
      filetype_kakunin_omote: "",
      image_kakunin_ura: "",
      filename_kakunin_ura: "",
      filetype_kakunin_ura: "",
      image_kakunin_atsumi: "",
      filename_kakunin_atsumi: "",
      filetype_kakunin_atsumi: "",
      image_license1: "",
      filename_license1: "",
      filetype_license1: "",
      image_license2: "",
      filename_license2: "",
      filetype_license2: "",
      image_license3: "",
      filename_license3: "",
      filetype_license3: "",
      sinkoku_jimusyo_code: "0",
      sinkoku_kosyu_kubun: 0,
      sinkoku_kosyu_date: "",
      sinkoku_kuwari_kubun: 0,
      unacquired_kosyu_code_title: "",
      sinkoku_jimusyo_code_title: "",
      sinkoku_kosyu_kubun_title: "",
      sinkoku_kuwari_kubun_title: "",
    );

    super.initState();
  }

// Method to initialize dropdown values from database data

  void initializeDropdownValues() {
    if (widget.p != null && !ref.read(dataInitializedProvider)) {
      // Set company option

      ref.read(optCompanyValueProvider.notifier).state =
          widget.p!.opt_company.toString();

      // If company is "当協会" (1), initialize the dropdown values

      if (widget.p!.opt_company == 1) {
        // Initialize jimusho

        if (widget.p!.sinkoku_jimusyo_code != "") {
          ref.read(jimushoValueProvider.notifier).state =
              widget.p!.sinkoku_jimusyo_code.toString();
        }

        // Initialize kubun

        if (widget.p!.sinkoku_kosyu_kubun != 0) {
          ref.read(kubunValueProvider.notifier).state =
              widget.p!.sinkoku_kosyu_kubun.toString();
        }

        // Initialize koushuu

        if (widget.p!.unacquired_kosyu_code != "") {
          ref.read(koushuuValueProvider.notifier).state =
              widget.p!.unacquired_kosyu_code.toString();
        }

        // Initialize koushuu date

        if (widget.p!.sinkoku_kosyu_date != "") {
          ref.read(koushuuDateValueProvider.notifier).state =
              widget.p!.sinkoku_kosyu_date;
        }

        // Initialize koushuu kuwari

        if (widget.p!.sinkoku_kuwari_kubun != 0) {
          ref.read(koushuuKuwariValueProvider.notifier).state =
              widget.p!.sinkoku_kuwari_kubun.toString();
        }
      }

      // Mark as initialized

      ref.read(dataInitializedProvider.notifier).state = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    // final String? appid = ref.watch(appStateProvider).applicationId;
    // final String? pid = ref.watch(appStateProvider).participantId;
    // final String? uid = ref.watch(appStateProvider).uketsukeId;

    final optValue = ref.watch(optValueProvider);
    final optCompanyValue = ref.watch(optCompanyValueProvider);
    final optSofuValue = ref.watch(optSofuValueProvider);

    //jimusho
    List<Map<String, dynamic>> lstJimusho = [];
    final fetchJimushoAsync = ref.watch(fetchJimushoListProvider);
    final selectJimushoValue = ref.watch(jimushoValueProvider);

    //koushuu kubun
    List<Map<String, dynamic>> lstKubun = [];
    final fetchKoushuuKubunAsync = selectJimushoValue != null
        ? ref.watch(fetchKoushuuKubunListProvider(selectJimushoValue))
        : const AsyncValue.data([]);
    final selectKoushuuKubunValue = ref.watch(kubunValueProvider);

    //koushuu
    List<Map<String, dynamic>> lstKoushuu = [];
    final selectKoushuuValue = ref.watch(koushuuValueProvider);
    final fetchKoushuuAsync =
        (selectJimushoValue != null && selectKoushuuKubunValue != null)
            ? ref.watch(fetchKoushuuListProvider(
                selectJimushoValue, selectKoushuuKubunValue))
            : const AsyncValue.data([]);

    //koushuu date
    final selectKoushuuDateValue = ref.watch(koushuuDateValueProvider);
    final fetchKoushuuDateAsync = (selectJimushoValue != null &&
            selectKoushuuKubunValue != null &&
            selectKoushuuValue != null)
        ? ref.watch(fetchKoushuuDateListProvider(
            selectJimushoValue, selectKoushuuKubunValue, selectKoushuuValue))
        : const AsyncValue.data([]);

    //koushuu kuwari
    List<Map<String, dynamic>> lstKoushuuKuwari = [];
    final selectKoushuuKuwariValue = ref.watch(koushuuKuwariValueProvider);
    final fetchKoushuuKuwariAsync = (selectJimushoValue != null &&
            selectKoushuuKubunValue != null &&
            selectKoushuuValue != null &&
            selectKoushuuDateValue != null)
        ? ref.watch(fetchKoushuuKuwariListProvider(
            selectJimushoValue,
            selectKoushuuKubunValue,
            selectKoushuuValue,
            selectKoushuuDateValue))
        : const AsyncValue.data([]);

    //if flgOut is 0, then uketsuke_id is empty because it directly from out sites
    if (widget.flgOut == 0) {
      uketsuke_id = "";
    } else {
      member_id = "";
    }

    final fetchParticipantInfo =
        ref.watch(getParticipantProvider(member_id, uketsuke_id));

    return Scaffold(
        body: fetchParticipantInfo.when(
          data: (dt) {
            //print(dt.toString());
            if (dt["status"] == constants.API_RES_SUCCESS) {
              if (dt["responsedata"] != "") {
                widget.p = Participant.fromJson(dt["responsedata"]);

                // ref.read(optValueProvider.notifier).state =
                //     widget.p?.old_or_common_name_type.toString() ?? "1";
                // ref.read(optCompanyValueProvider.notifier).state =
                //     widget.p?.opt_company ?? "1";

                // final selectedVal = widget.p?.declaration_party_name == ""
                //     ? widget.p?.sinkoku_jimusyo_code_title == ""
                //         ? "0"
                //         : "1"
                //     : "2";
                // ref.read(optCompanyValueProvider.notifier).state = selectedVal;

                // データ取得後にフォームへ反映
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (!ref.read(dataInitializedProvider)) {
                    ref.read(optCompanyValueProvider.notifier).state =
                        widget.p?.opt_company?.toString() ?? "0";
                    initializeDropdownValues();

                    // if (widget.p?.opt_company?.toString() == "1") {
                    //   initializeDropdownValues();
                    // }
                  }
                });
              }
            }

            return FormBuilder(
              key: _formKey,
              // initialValue: {
              //   "koushuu": widget.p?.sinkoku_jimusyo_code.toString(),
              // },
              child: SingleChildScrollView(
                controller: _scrollController,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                    const Row(
                      children: [
                        TopBar(
                          title: '受講者情報登録',
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                    personCnt != 0
                        ? Padding(
                            padding:
                                const EdgeInsets.only(left: 20.0, right: 20.0),
                            child: Row(
                                // crossAxisAlignment: CrossAxisAlignment.stretch,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  DefaultText(
                                    txt: '【$personCnt人目】',
                                  ),
                                ]),
                          )
                        : const SizedBox(
                            height: 10,
                          ),
                    Padding(
                      padding: const EdgeInsets.only(left: 20.0, right: 20.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Column(
                              children: [
                                const Text("※オレンジの枠は必須項目です。"),
                                const SizedBox(
                                  height: 10,
                                ),
                                _buildTextField(
                                  "name1",
                                  "名称1",
                                  false,
                                  FormBuilderValidators.compose([
                                    FormBuilderValidators.required(),
                                  ]),
                                  true,
                                  initialValue: widget.p?.name1,
                                  placeHolder: "安全　太郎（姓名の間は空白）",
                                ),
                                _buildTextField(
                                  "name2",
                                  "名称2",
                                  false,
                                  null,
                                  // FormBuilderValidators.compose([
                                  //   FormBuilderValidators.required(),
                                  // ]),
                                  false,
                                  initialValue: widget.p?.name2,
                                  placeHolder: "氏名1に入力しきれない方",
                                ),
                                _buildTextField(
                                  "furigana",
                                  "ふりがな",
                                  false,
                                  FormBuilderValidators.compose([
                                    FormBuilderValidators.required(),
                                  ]),
                                  true,
                                  initialValue: widget.p?.name_kana,
                                  placeHolder: "アンゼンタロウ",
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: 0.0, right: 0, top: 20, bottom: 0),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                        child: FormBuilderRadioGroup<String>(
                                          decoration: const InputDecoration(
                                            border: OutlineInputBorder(),
                                            labelText: "理由",
                                          ),
                                          wrapAlignment: WrapAlignment.start,
                                          name: 'optReason',
                                          initialValue: widget.p != null
                                              ? widget.p?.old_or_common_name_type ==
                                                      0
                                                  ? "1"
                                                  : widget.p
                                                      ?.old_or_common_name_type
                                                      .toString()
                                              : "1",
                                          options: const [
                                            FormBuilderFieldOption(
                                                value: '1', child: Text('なし')),
                                            FormBuilderFieldOption(
                                                value: '2', child: Text('旧姓')),
                                            FormBuilderFieldOption(
                                                value: '3', child: Text('通作')),
                                          ],
                                          validator:
                                              FormBuilderValidators.compose([
                                            FormBuilderValidators.required(),
                                          ]),
                                          onChanged: (val) {
                                            ref
                                                .read(optValueProvider.notifier)
                                                .state = val!;
                                            if (val == "1") {
                                              _formKey.currentState
                                                  ?.fields['reason']
                                                  ?.didChange("");
                                            }
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 5.0),
                                  child: FormBuilderTextField(
                                    name: "reason",
                                    enabled: optValue != "1",
                                    obscureText: false,
                                    initialValue: widget.p?.reason ?? "",
                                    validator: FormBuilderValidators.compose(
                                        optValue != "1"
                                            ? [
                                                FormBuilderValidators
                                                    .required(),
                                              ]
                                            : []),
                                    decoration: InputDecoration(
                                      filled: optValue != "1",
                                      fillColor: optValue != "1"
                                          ? Colors.orangeAccent.shade100
                                          : Colors.transparent,
                                      labelText: "理由",
                                      border: const OutlineInputBorder(),
                                    ),
                                  ),
                                ),
                                _buildTextField(
                                  "birthday",
                                  "生年月日",
                                  false,
                                  FormBuilderValidators.compose([
                                    FormBuilderValidators.required(),
                                    FormBuilderValidators.match(
                                      RegExp(r'^\d{4}/\d{1,2}/\d{1,2}$'),
                                      errorText: "YYYY/MM/DDフォーマットで入力して下さい。",
                                    ),
                                  ]),
                                  true,
                                  inputFormatter: [
                                    FilteringTextInputFormatter.allow(
                                        RegExp(r'[/]|[0-9]')),
                                    LengthLimitingTextInputFormatter(10),
                                  ],
                                  initialValue: widget.p?.birth_day,
                                  placeHolder: "2000/01/01",
                                ),
                                Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 5.0),
                                  child: FormBuilderTextField(
                                    name: "postal",
                                    enabled: true,
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                          RegExp(r'[-]|[0-9]')),
                                      LengthLimitingTextInputFormatter(8),
                                    ],
                                    validator: FormBuilderValidators.compose(
                                      [
                                        FormBuilderValidators.required(),
                                        FormBuilderValidators.match(
                                            RegExp(r'[-]|[0-9]')),
                                      ],
                                    ),
                                    initialValue: widget.p?.zip_code,
                                    decoration: InputDecoration(
                                      filled: true,
                                      fillColor: Colors.orangeAccent.shade100,
                                      labelText: "〒",
                                      hintText: "136-0071",
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.always,
                                      hintStyle:
                                          const TextStyle(color: Colors.grey),
                                      border: const OutlineInputBorder(),
                                    ),
                                    onChanged: (value) async {
                                      if (value != null && value.length >= 7) {
                                        context.loaderOverlay.show();
                                        final res = await ref
                                            .read(addressRepositoryProvider)
                                            .fetchData(value);

                                        context.loaderOverlay.hide();

                                        if (res.city != "") {
                                          _formKey.currentState?.fields['addr1']
                                              ?.didChange(res.pref +
                                                  res.city +
                                                  res.area);
                                          // _formKey.currentState?.fields['addr2']
                                          //     ?.didChange(res.city + res.area);
                                        } else {
                                          ScaffoldMessenger.of(context)
                                              .showSnackBar(
                                            const SnackBar(
                                                content: Text('住所見つかりませんでした。')),
                                          );
                                          _formKey.currentState?.fields['addr1']
                                              ?.didChange("");
                                          _formKey.currentState?.fields['addr2']
                                              ?.didChange("");
                                        }
                                      }
                                    },
                                    // onSubmitted: (value) async {
                                    //   if (value!.length >= 7) {
                                    //     context.loaderOverlay.show();
                                    //     final res = await ref
                                    //         .read(addressRepositoryProvider)
                                    //         .fetchData(value);

                                    //     context.loaderOverlay.hide();

                                    //     if (res.city != "") {
                                    //       _formKey.currentState?.fields['addr1']
                                    //           ?.didChange(res.pref);
                                    //       _formKey.currentState?.fields['addr2']
                                    //           ?.didChange(res.city + res.area);
                                    //       //_formKey.currentState!.save();
                                    //     } else {
                                    //       ScaffoldMessenger.of(context)
                                    //           .showSnackBar(
                                    //         const SnackBar(
                                    //             content: Text('住所見つかりませんでした。')),
                                    //       );
                                    //       _formKey.currentState?.fields['addr1']
                                    //           ?.didChange("");
                                    //       _formKey.currentState?.fields['addr2']
                                    //           ?.didChange("");
                                    //     }
                                    //   }
                                    // },
                                  ),
                                ),

                                _buildTextField(
                                  "addr1",
                                  "住所１",
                                  false,
                                  FormBuilderValidators.compose([
                                    FormBuilderValidators.required(),
                                  ]),
                                  true,
                                  initialValue: widget.p?.addr1,
                                  placeHolder: "東京都江東区戸6-41-20",
                                ),
                                _buildTextField(
                                  "addr2",
                                  "住所２",
                                  false,
                                  null,
                                  // FormBuilderValidators.compose([
                                  //   FormBuilderValidators.required(),
                                  // ]),
                                  false,
                                  initialValue: widget.p?.addr2,
                                  placeHolder: "機缶健保会館2階",
                                ),
                                _buildTextField(
                                  "tel1",
                                  "電話番号",
                                  false,
                                  FormBuilderValidators.compose([
                                    FormBuilderValidators.required(),
                                  ]),
                                  true,
                                  inputFormatter: [
                                    FilteringTextInputFormatter.allow(
                                        RegExp(r'[-]|[0-9]')),
                                    LengthLimitingTextInputFormatter(14),
                                  ],
                                  initialValue: widget.p?.tel_no,
                                  placeHolder: "0336852141",
                                ),
                                _buildTextField(
                                  "mail",
                                  "メールアドレス",
                                  false,
                                  FormBuilderValidators.compose([
                                    FormBuilderValidators.required(),
                                    FormBuilderValidators.email(),
                                  ]),
                                  true,
                                  initialValue: widget.p?.mail_addr,
                                  placeHolder: "anzen@◯△x.co.jp",
                                ),
                                // Add re_email field
                                _buildTextField(
                                  "re_email",
                                  "メールアドレス（確認用）",
                                  false,
                                  (value) {
                                    if (value == null || value.isEmpty) {
                                      return '確認用メールアドレスを入力してください';
                                    }
                                    final email = _formKey
                                        .currentState?.fields['mail']?.value;
                                    if (value != email) {
                                      return 'メールアドレスが一致しません';
                                    }
                                    return null;
                                  },
                                  true,
                                  enableInteractiveSelection: false,
                                  placeHolder: "anzen@◯△x.co.jp（確認用）",
                                ),
                                const Divider(),
                                _buildPhotoField("photo1", '本人写真',
                                    widget.p?.image_photo ?? "",
                                    filename: widget.p?.filename_photo ?? "",
                                    filetype: widget.p?.filetype_photo ?? ""),
                                _buildFileField("photo2", '本人確認書類　表',
                                    widget.p?.image_kakunin_omote ?? "",
                                    filename:
                                        widget.p?.filename_kakunin_omote ?? "",
                                    filetype:
                                        widget.p?.filetype_kakunin_omote ?? ""),
                                _buildFileField("photo3", '本人確認書類　裏',
                                    widget.p?.image_kakunin_ura ?? "",
                                    filename:
                                        widget.p?.filename_kakunin_ura ?? "",
                                    filetype:
                                        widget.p?.filetype_kakunin_ura ?? ""),
                                const Divider(),
                                _buildFileField("photo5", '資格１',
                                    widget.p?.image_license1 ?? "",
                                    filename: widget.p?.filename_license1 ?? "",
                                    filetype:
                                        widget.p?.filetype_license1 ?? ""),
                                _buildFileField("photo6", '資格２',
                                    widget.p?.image_license2 ?? "",
                                    filename: widget.p?.filename_license2 ?? "",
                                    filetype:
                                        widget.p?.filetype_license2 ?? ""),
                                _buildFileField("photo7", '資格３',
                                    widget.p?.image_license3 ?? "",
                                    filename: widget.p?.filename_license3 ?? "",
                                    filetype:
                                        widget.p?.filetype_license3 ?? ""),
                                const Padding(
                                  padding: EdgeInsets.only(
                                      left: 20.0,
                                      right: 20,
                                      top: 20,
                                      bottom: 0),
                                  child: DefaultText(
                                    txt: "※これから取得予定の方は以下に申告入力してください",
                                    fontSize: 16,
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(top: 20),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                        child: FormBuilderRadioGroup<String>(
                                          wrapAlignment: WrapAlignment.start,
                                          name: 'optCompany',
                                          initialValue:
                                              widget.p?.opt_company.toString(),

                                          //         ?.declaration_party_name ==
                                          //     ""
                                          // ? widget.p?.sinkoku_jimusyo_code_title ==
                                          //         ""
                                          //     ? "0"
                                          //     : "1"
                                          // : "2",
                                          options: const [
                                            FormBuilderFieldOption(
                                                value: '0', child: Text('無し')),
                                            FormBuilderFieldOption(
                                                value: '1', child: Text('当協会')),
                                            FormBuilderFieldOption(
                                                value: '2', child: Text('他団体')),
                                          ],
                                          validator:
                                              FormBuilderValidators.compose([
                                            FormBuilderValidators.required(),
                                          ]),
                                          onChanged: (val) {
                                            ref
                                                .read(optCompanyValueProvider
                                                    .notifier)
                                                .state = val!;
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const FieldTitle(
                                  title: "↓ボイラクレーン安全協会の場合",
                                  txtColor: Colors.red,
                                ),
                                //Jimusho list
                                // const JimushoListUi(name: "jimusho"),
                                const SizedBox(
                                  height: 5,
                                ),
                                fetchJimushoAsync.when(
                                  data: (val) {
                                    lstJimusho.clear();
                                    lstJimusho.addAll(val);

                                    return FormBuilderDropdown<String>(
                                      name: "jimusho",
                                      enabled: optCompanyValue == "1",
                                      initialValue: selectJimushoValue,
                                      onChanged: (newValue) {
                                        ref
                                            .read(jimushoValueProvider.notifier)
                                            .state = newValue!;
                                      },
                                      items: val.map((it) {
                                        //lstJimusho.add(it);
                                        return DropdownMenuItem<String>(
                                          value: it["jimusyo_code"].toString(),
                                          child: Text(it["jimusyo_name"]),
                                        );
                                      }).toList(),
                                      isExpanded: true,
                                      validator: FormBuilderValidators.compose(
                                          optCompanyValue == "1"
                                              ? [
                                                  FormBuilderValidators
                                                      .required(),
                                                ]
                                              : []),
                                      decoration: InputDecoration(
                                        filled: optCompanyValue == "1",
                                        fillColor: optCompanyValue == "1"
                                            ? Colors.orangeAccent.shade100
                                            : Colors.transparent,
                                        border: const OutlineInputBorder(),
                                        labelText: "事務所",
                                      ),
                                    );
                                  },
                                  loading: () => const Center(
                                      child: CircularProgressIndicator()),
                                  error: (e, _) => ErrorInfo(txt: e.toString()),
                                ),

                                const SizedBox(
                                  height: 10,
                                ),
                                fetchKoushuuKubunAsync.when(
                                  data: (val) {
                                    lstKubun.clear();
                                    //lstKubun.addAll(val);
                                    return FormBuilderDropdown<String>(
                                      name: "kubun",
                                      enabled: optCompanyValue == "1",
                                      initialValue: selectKoushuuKubunValue,
                                      onChanged: (String? newValue) {
                                        ref
                                            .read(kubunValueProvider.notifier)
                                            .state = newValue!;
                                      },
                                      items: val.map((it) {
                                        lstKubun.add(it);
                                        return DropdownMenuItem<String>(
                                          value: it["kosyu_kubun"].toString(),
                                          // value: int.parse(it["kosyu_kubun"])
                                          //     .toString(),
                                          child: Text(it["kosyu_kubun_name"]),
                                        );
                                      }).toList(),
                                      isExpanded: true,
                                      validator: FormBuilderValidators.compose(
                                          optCompanyValue == "1"
                                              ? [
                                                  FormBuilderValidators
                                                      .required(),
                                                ]
                                              : []),
                                      decoration: InputDecoration(
                                        filled: optCompanyValue == "1",
                                        fillColor: optCompanyValue == "1"
                                            ? Colors.orangeAccent.shade100
                                            : Colors.transparent,
                                        border: const OutlineInputBorder(),
                                        labelText: "講習区分",
                                      ),
                                    );
                                  },
                                  loading: () => const Center(
                                      child: CircularProgressIndicator()),
                                  error: (e, _) => ErrorInfo(txt: e.toString()),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                fetchKoushuuAsync.when(
                                  data: (val) {
                                    lstKoushuu.clear();
                                    return FormBuilderDropdown<String>(
                                      name: "koushuu",
                                      enabled: optCompanyValue == "1",
                                      initialValue: selectKoushuuValue,
                                      // initialValue:
                                      //     widget.p?.unacquired_kosyu_code != 0
                                      //         ? widget.p?.unacquired_kosyu_code
                                      //             .toString()
                                      //         : null,
                                      onChanged: (newValue) {
                                        ref
                                            .read(koushuuValueProvider.notifier)
                                            .state = newValue!;
                                      },
                                      items: val.map((it) {
                                        lstKoushuu.add(it);
                                        return DropdownMenuItem<String>(
                                          value: it["kosyu_code"],
                                          // value: int.parse(it["kosyu_code"])
                                          //     .toString(),
                                          child: Text(it["kosyu_name"]),
                                        );
                                      }).toList(),
                                      isExpanded: true,
                                      validator: FormBuilderValidators.compose(
                                          optCompanyValue == "1"
                                              ? [
                                                  FormBuilderValidators
                                                      .required(),
                                                ]
                                              : []),
                                      decoration: InputDecoration(
                                        filled: optCompanyValue == "1",
                                        fillColor: optCompanyValue == "1"
                                            ? Colors.orangeAccent.shade100
                                            : Colors.transparent,
                                        border: const OutlineInputBorder(),
                                        labelText: "講習",
                                      ),
                                    );
                                  },
                                  loading: () => const Center(
                                      child: CircularProgressIndicator()),
                                  error: (e, _) => ErrorInfo(txt: e.toString()),
                                ),

                                const SizedBox(
                                  height: 10,
                                ),
                                fetchKoushuuDateAsync.when(
                                  data: (val) {
                                    return FormBuilderDropdown<String>(
                                      name: "koushuu_date",
                                      enabled: optCompanyValue == "1",
                                      initialValue: selectKoushuuDateValue,
                                      // initialValue:
                                      //     widget.p?.sinkoku_kosyu_date,
                                      onChanged: (String? newValue) {
                                        ref
                                            .read(koushuuDateValueProvider
                                                .notifier)
                                            .state = newValue!;
                                      },
                                      items: val.map((it) {
                                        return DropdownMenuItem<String>(
                                          value: it["kosyu_date"],
                                          child: Text(it["kosyu_date"]),
                                        );
                                      }).toList(),
                                      isExpanded: true,
                                      validator: FormBuilderValidators.compose(
                                          optCompanyValue == "1"
                                              ? [
                                                  FormBuilderValidators
                                                      .required(),
                                                ]
                                              : []),
                                      decoration: InputDecoration(
                                        filled: optCompanyValue == "1",
                                        fillColor: optCompanyValue == "1"
                                            ? Colors.orangeAccent.shade100
                                            : Colors.transparent,
                                        border: const OutlineInputBorder(),
                                        labelText: "受講日",
                                      ),
                                    );
                                  },
                                  loading: () => const Center(
                                      child: CircularProgressIndicator()),
                                  error: (e, _) => ErrorInfo(txt: e.toString()),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                fetchKoushuuKuwariAsync.when(
                                  data: (val) {
                                    lstKoushuuKuwari.clear();
                                    return FormBuilderDropdown<String>(
                                      name: "koushuu_kuwari",
                                      enabled: optCompanyValue == "1",
                                      initialValue: selectKoushuuKuwariValue,
                                      // initialValue:
                                      //     widget.p?.sinkoku_kuwari_kubun != 0
                                      //         ? widget.p?.sinkoku_kuwari_kubun
                                      //             .toString()
                                      //         : null,
                                      onChanged: (String? newValue) {
                                        ref
                                            .read(koushuuKuwariValueProvider
                                                .notifier)
                                            .state = newValue!;
                                      },
                                      items: val.map((it) {
                                        lstKoushuuKuwari.add(it);
                                        return DropdownMenuItem<String>(
                                          value: it["kuwari_kubun"].toString(),
                                          // value: int.parse(it["kuwari_kubun"])
                                          //     .toString(),
                                          child: Text(it["kuwari_kubun_name"]),
                                        );
                                      }).toList(),
                                      isExpanded: true,
                                      decoration: const InputDecoration(
                                        border: OutlineInputBorder(),
                                        labelText: "区割区分",
                                      ),
                                    );
                                  },
                                  loading: () => const Center(
                                      child: CircularProgressIndicator()),
                                  error: (e, _) => ErrorInfo(txt: e.toString()),
                                ),
                                const SizedBox(
                                  height: 20,
                                ),
                                const Divider(),
                                const SizedBox(
                                  height: 20,
                                ),
                                const FieldTitle(
                                  title: "↓他団体の場合",
                                  txtColor: Colors.red,
                                ),
                                _buildTextField(
                                  "dantai",
                                  "団体名",
                                  false,
                                  FormBuilderValidators.compose(
                                      optCompanyValue == "2"
                                          ? [
                                              FormBuilderValidators.required(),
                                            ]
                                          : []),
                                  optCompanyValue == "2",
                                  enabled: optCompanyValue == "2",
                                  initialValue:
                                      widget.p?.declaration_party_name,
                                  placeHolder: "安全〇A協会",
                                ),
                                _buildTextField(
                                  "enddate",
                                  "交付日/修了日",
                                  false,
                                  FormBuilderValidators.compose(
                                      optCompanyValue == "2"
                                          ? [
                                              FormBuilderValidators.required(),
                                              // FormBuilderValidators.match(RegExp(
                                              //     r'^\d{4}/\d{1,2}/\d{1,2}$')),
                                              // FormBuilderValidators.date()
                                            ]
                                          : []),
                                  optCompanyValue == "2",
                                  enabled: optCompanyValue == "2",
                                  initialValue: widget.p?.declaration_date,
                                  placeHolder: "2000/01/01",
                                ),
                                const SizedBox(
                                  height: 20,
                                ),
                                const Divider(),
                                const SizedBox(
                                  height: 20,
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: FormBuilderRadioGroup<String>(
                                        decoration: const InputDecoration(
                                          border: OutlineInputBorder(),
                                          labelText: "修了証送付先",
                                        ),
                                        wrapAlignment: WrapAlignment.start,
                                        name: 'optSofu',
                                        initialValue: widget.p != null
                                            ? widget.p?.soufu_kubun == 0
                                                ? "1"
                                                : widget.p?.soufu_kubun
                                                    .toString()
                                            : "1",
                                        options: const [
                                          FormBuilderFieldOption(
                                              value: '1', child: Text('申込会社')),
                                          FormBuilderFieldOption(
                                              value: '2', child: Text('自宅')),
                                        ],
                                        separator: null,
                                        validator:
                                            FormBuilderValidators.compose([
                                          FormBuilderValidators.required(),
                                        ]),
                                        onChanged: (val) {
                                          ref
                                              .read(
                                                  optSofuValueProvider.notifier)
                                              .state = val!;
                                        }, // 初期選択
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 20.0, right: 20.0),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CoreButton(
                            title: "次へ",
                            onPressed: () async {
                              //print("next clicked");
                              if (_formKey.currentState!.saveAndValidate(
                                  autoScrollWhenFocusOnInvalid: true)) {
                                context.loaderOverlay.show();
                                try {
                                  //print("next clicked1");
                                  final name1Value = _formKey.currentState
                                          ?.fields['name1']?.value ??
                                      "";
                                  final name2Value = _formKey.currentState
                                          ?.fields['name2']?.value ??
                                      "";
                                  final furiganaValue = _formKey.currentState
                                          ?.fields['furigana']?.value ??
                                      "";
                                  final reasonTitle = _formKey.currentState
                                          ?.fields['reason']?.value ??
                                      "";
                                  final birthdayValue = _formKey.currentState
                                          ?.fields['birthday']?.value ??
                                      "";
                                  final postalValue = _formKey.currentState
                                          ?.fields['postal']?.value ??
                                      "";
                                  final addr1Value = _formKey.currentState
                                          ?.fields['addr1']?.value ??
                                      "";
                                  final addr2Value = _formKey.currentState
                                          ?.fields['addr2']?.value ??
                                      "";
                                  final tel1Value = _formKey.currentState
                                          ?.fields['tel1']?.value ??
                                      "";
                                  final emailValue = _formKey.currentState
                                          ?.fields['mail']?.value ??
                                      "";
                                  final dantaiValue = _formKey.currentState
                                          ?.fields['dantai']?.value ??
                                      "";
                                  final enddateValue = _formKey.currentState
                                          ?.fields['enddate']?.value ??
                                      "";

                                  //print("next clicked2");
                                  //Photo1
                                  final photo1Images = _formKey
                                          .currentState!
                                          .fields['photo1']
                                          ?.value as List<PlatformFile>? ??
                                      [];
                                  final photo1Data =
                                      await doPrepareFileData(photo1Images);
                                  base64photo1 = photo1Data["fdata"];
                                  base64photo1Name = photo1Data["fname"];
                                  base64photo1Type = photo1Data["ftype"];
                                  //Photo2
                                  final photo2Images = _formKey
                                          .currentState!
                                          .fields['photo2']
                                          ?.value as List<PlatformFile>? ??
                                      [];
                                  final photo2Data =
                                      await doPrepareFileData(photo2Images);
                                  base64photo2 = photo2Data["fdata"];
                                  base64photo2Name = photo2Data["fname"];
                                  base64photo2Type = photo2Data["ftype"];

                                  //Photo3
                                  final photo3Images = _formKey
                                          .currentState!
                                          .fields['photo3']
                                          ?.value as List<PlatformFile>? ??
                                      [];
                                  final photo3Data =
                                      await doPrepareFileData(photo3Images);
                                  base64photo3 = photo3Data["fdata"];
                                  base64photo3Name = photo3Data["fname"];
                                  base64photo3Type = photo3Data["ftype"];

                                  //Photo4
                                  // final photo4Images = _formKey
                                  //     .currentState!.fields['photo4']?.value;
                                  // final photo4Data =
                                  //     await doPrepareImageData(photo4Images);
                                  // base64photo4 = photo4Data["fdata"];
                                  // base64photo4Name = photo4Data["fname"];
                                  // base64photo4Type = photo4Data["ftype"];

                                  //Photo5
                                  final photo5Images = _formKey
                                          .currentState!
                                          .fields['photo5']
                                          ?.value as List<PlatformFile>? ??
                                      [];
                                  final photo5Data =
                                      await doPrepareFileData(photo5Images);
                                  base64photo5 = photo5Data["fdata"];
                                  base64photo5Name = photo5Data["fname"];
                                  base64photo5Type = photo5Data["ftype"];

                                  //Photo6
                                  final photo6Images = _formKey
                                          .currentState!
                                          .fields['photo6']
                                          ?.value as List<PlatformFile>? ??
                                      [];

                                  final photo6Data =
                                      await doPrepareFileData(photo6Images);
                                  base64photo6 = photo6Data["fdata"];
                                  base64photo6Name = photo6Data["fname"];
                                  base64photo6Type = photo6Data["ftype"];

                                  //Photo7
                                  final photo7Images = _formKey
                                          .currentState!
                                          .fields['photo7']
                                          ?.value as List<PlatformFile>? ??
                                      [];

                                  final photo7Data =
                                      await doPrepareFileData(photo7Images);
                                  base64photo7 = photo7Data["fdata"];
                                  base64photo7Name = photo7Data["fname"];
                                  base64photo7Type = photo7Data["ftype"];

                                  //print("next clicked3");
                                  String t_sinkoku_jimusyo_title = "";
                                  String t_sinkoku_kosyu_kubun_title = "";
                                  String t_unacquired_kosyu_title = "";
                                  String t_sinkoku_kuwari_kubun_title = "";
                                  String unacquired_kosyu_code = "0";
                                  String sinkoku_jimusyo_code = "0";
                                  String sinkoku_kosyu_kubun = "0";
                                  String sinkoku_kosyu_date = "";
                                  String sinkoku_kuwari_kubun = "0";

                                  String declaration_party_name = "";
                                  String declaration_date = "";

                                  if (ref
                                          .read(
                                              optCompanyValueProvider.notifier)
                                          .state ==
                                      "1") {
                                    t_sinkoku_jimusyo_title =
                                        Utils.getKeyValueFromMapList(
                                            "jimusyo_code",
                                            selectJimushoValue.toString(),
                                            "jimusyo_name",
                                            lstJimusho);

                                    t_sinkoku_kosyu_kubun_title =
                                        Utils.getKeyValueFromMapList(
                                            "kosyu_kubun",
                                            selectKoushuuKubunValue.toString(),
                                            "kosyu_kubun_name",
                                            lstKubun);

                                    t_unacquired_kosyu_title =
                                        Utils.getKeyValueFromMapList(
                                            "kosyu_code",
                                            selectKoushuuValue.toString(),
                                            "kosyu_name",
                                            lstKoushuu);

                                    t_sinkoku_kuwari_kubun_title =
                                        Utils.getKeyValueFromMapList(
                                            "kuwari_kubun",
                                            selectKoushuuKuwariValue.toString(),
                                            "kuwari_kubun_name",
                                            lstKoushuuKuwari);

                                    unacquired_kosyu_code =
                                        selectKoushuuValue ?? "0";
                                    sinkoku_jimusyo_code =
                                        selectJimushoValue ?? "0";
                                    sinkoku_kosyu_kubun =
                                        selectKoushuuKubunValue ?? "0";
                                    sinkoku_kosyu_date =
                                        selectKoushuuDateValue ?? "";
                                    sinkoku_kuwari_kubun =
                                        selectKoushuuKuwariValue ?? "0";
                                  } else if (ref
                                          .read(
                                              optCompanyValueProvider.notifier)
                                          .state ==
                                      "2") {
                                    declaration_party_name = dantaiValue;
                                    declaration_date = enddateValue;
                                  }

                                  //print("next clicked4");
                                  widget.p = Participant(
                                    id: int.parse(member_id),
                                    application_id: int.parse(application_id),
                                    uketsuke_id: uketsuke_id,
                                    kosyu_number: '',
                                    kosyu_code: 0,
                                    kosyu_date: '',
                                    course_code: 0,
                                    jimusyo_code: 0,
                                    kaijyo_code: 0,
                                    applicant_number: member_id.toString(),
                                    name1: name1Value.toString(),
                                    name2: name2Value.toString(),
                                    name_kana: furiganaValue,
                                    old_or_common_name_type:
                                        int.parse(optValue),
                                    reason: reasonTitle,
                                    name3: '',
                                    birth_day: birthdayValue,
                                    zip_code: postalValue,
                                    addr1: addr1Value,
                                    addr2: addr2Value,
                                    tel_no: tel1Value,
                                    mail_addr: emailValue,
                                    opt_company: int.parse(optCompanyValue),
                                    declaration_party_name:
                                        declaration_party_name,
                                    declaration_date: declaration_date,
                                    soufu_kubun: int.parse(optSofuValue),
                                    url: '',
                                    image_photo: base64photo1,
                                    filename_photo: base64photo1Name,
                                    filetype_photo: base64photo1Type,
                                    image_kakunin_omote: base64photo2,
                                    filename_kakunin_omote: base64photo2Name,
                                    filetype_kakunin_omote: base64photo2Type,
                                    image_kakunin_ura: base64photo3,
                                    filename_kakunin_ura: base64photo3Name,
                                    filetype_kakunin_ura: base64photo3Type,
                                    image_kakunin_atsumi: base64photo4,
                                    filename_kakunin_atsumi: base64photo4Name,
                                    filetype_kakunin_atsumi: base64photo4Type,
                                    image_license1: base64photo5,
                                    filename_license1: base64photo5Name,
                                    filetype_license1: base64photo5Type,
                                    image_license2: base64photo6,
                                    filename_license2: base64photo6Name,
                                    filetype_license2: base64photo6Type,
                                    image_license3: base64photo7,
                                    filename_license3: base64photo7Name,
                                    filetype_license3: base64photo7Type,
                                    unacquired_kosyu_code:
                                        unacquired_kosyu_code,
                                    sinkoku_jimusyo_code: sinkoku_jimusyo_code,
                                    sinkoku_kosyu_kubun:
                                        int.parse(sinkoku_kosyu_kubun),
                                    sinkoku_kosyu_date: sinkoku_kosyu_date,
                                    sinkoku_kuwari_kubun:
                                        int.parse(sinkoku_kuwari_kubun),
                                    unacquired_kosyu_code_title:
                                        t_unacquired_kosyu_title,
                                    sinkoku_jimusyo_code_title:
                                        t_sinkoku_jimusyo_title,
                                    sinkoku_kosyu_kubun_title:
                                        t_sinkoku_kosyu_kubun_title,
                                    sinkoku_kuwari_kubun_title:
                                        t_sinkoku_kuwari_kubun_title,
                                  );

                                  //print("next clicked5");
                                  context.loaderOverlay.hide();

                                  //print("next clicked6");
                                  showModalBottomSheet(
                                    useSafeArea: true,
                                    isScrollControlled: true,
                                    context: context,
                                    builder: (ctx) => CheckParticipantPage(
                                      title: '受講者情報登録（確認）',
                                      p: widget.p,
                                      flgView: 0,
                                      flgOut: 1, //widget.flgOut,
                                    ),
                                  );
                                } catch (e, stackTrace) {
                                  print('Error: $e');
                                  print('StackTrace: $stackTrace');
                                  context.loaderOverlay.hide();
                                }
                              }
                            },
                            bgColor: Colors.red,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 40,
                    ),
                  ],
                ),
              ),
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (e, _) => const ErrorInfo(txt: "データ読み込みできませんでした！"),
        ),
        floatingActionButton: UpFabButton(
          scrollViewController: _scrollController,
        ),
        bottomNavigationBar: const BottomBar(title: ''));
  }

  // Future<void> prepareImageData(
  //   selectedImages,
  //   f64data,
  //   fname,
  //   ftype,
  // ) async {
  //   if (selectedImages != null && selectedImages.isNotEmpty) {
  //     // Get selected image
  //     final imageFile = selectedImages[0];
  //     if (imageFile != null) {
  //       fname = imageFile.name;
  //       ftype = imageFile.mimeType;

  //       // 画像をBase64にエンコード
  //       await imageFile.readAsBytes().then((bytes) {
  //         f64data = base64Encode(bytes);
  //         // debugPrint("Base64 Image: $f64data");
  //       }).catchError((e) {
  //         debugPrint("Error reading image file: $e");
  //         f64data = "";
  //       });
  //     }
  //   }
  // }

  // Future<Map<String, dynamic>> doPrepareImageData(selectedImages) async {
  //   String fname = "";
  //   String ftype = "";
  //   String fdata = "";

  //   if (selectedImages != null && selectedImages.isNotEmpty) {
  //     // Get selected image
  //     final imageFile = selectedImages[0];
  //     if (imageFile != null) {
  //       fname = imageFile.name;
  //       ftype = imageFile.mimeType!;

  //       // 画像をBase64にエンコード
  //       await imageFile.readAsBytes().then((bytes) {
  //         fdata = base64Encode(bytes);
  //         // debugPrint("Base64 Image: $f64data");
  //       }).catchError((e) {
  //         debugPrint("Error reading image file: $e");
  //       });
  //     }
  //   }
  //   return {
  //     "fname": fname,
  //     "ftype": ftype,
  //     "fdata": fdata,
  //   };
  // }

  Widget _buildTextField(
    String name,
    String label,
    bool isSecure,
    String? Function(String?)? validator,
    bool? isRequired, {
    bool? enabled,
    List<TextInputFormatter>? inputFormatter,
    String? initialValue,
    bool? enableInteractiveSelection, // テキスト選択禁止
    String? placeHolder,
  }) {
    isRequired ??= false;
    enabled ??= true;
    enableInteractiveSelection ??= true;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5.0),
      child: FormBuilderTextField(
        name: name,
        initialValue: initialValue,
        enableInteractiveSelection: enableInteractiveSelection, // テキスト選択禁止
        enabled: enabled,
        obscureText: isSecure,
        inputFormatters: inputFormatter == [] ? null : inputFormatter,
        validator: validator,
        decoration: InputDecoration(
          filled: isRequired,
          fillColor:
              isRequired ? Colors.orangeAccent.shade100 : Colors.transparent,
          labelText: label,
          hintText: placeHolder,
          hintStyle: const TextStyle(color: Colors.grey),
          floatingLabelBehavior: FloatingLabelBehavior.always,
          border: const OutlineInputBorder(),
        ),
      ),
    );
  }

  // Widget _buildPhotoField(String fieldName, String lbl, String base64data,
  //     {String filename = "", String filetype = ""}) {
  //   return Padding(
  //     padding: const EdgeInsets.all(8.0),
  //     child: FormBuilderImagePicker(
  //       name: fieldName,
  //       maxImages: 1,
  //       previewAutoSizeWidth: true,
  //       decoration: InputDecoration(labelText: lbl),
  //       backgroundColor: Colors.black54,
  //       iconColor: Colors.white,
  //       icon: Icons.camera_alt_outlined,
  //       initialValue: base64data != "" ? [base64Decode(base64data)] : [null],
  //       cameraLabel: const Text('カメラ'),
  //       galleryLabel: const Text('写真ライブラリ'),
  //       // validator: FormBuilderValidators.compose([
  //       //   FormBuilderValidators.required(),
  //       // ]),
  //     ),
  //   );
  // }

  Widget _buildFileField(String fieldName, String lbl, String base64data,
      {String filename = "", String filetype = ""}) {
    var pf = null;
    if (base64data != "") {
      pf = CustomFileField.platformFileFromBase64(base64data, name: filename);
    }

    return Padding(
      padding: const EdgeInsets.all(8.0),
      //child: CustomStyledFilePicker(label: lbl, filename: fieldName),
      //child: CustomFilePicker(filename: fieldName),

      child: CustomFileField(
        fieldName,
        name: fieldName,
        label: lbl,
        initValues: pf != null ? [pf] : [],
      ),
    );
  }

  Widget _buildPhotoField(String fieldName, String lbl, String base64data,
      {String filename = "", String filetype = ""}) {
    var pf = null;
    if (base64data != "") {
      pf = CustomFileField.platformFileFromBase64(base64data, name: filename);
    }

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: CustomFileField(
        fieldName,
        name: fieldName,
        label: lbl,
        initValues: pf != null ? [pf] : [],
        allowedExtensions: const ['jpg', 'jpeg', 'png'],
      ),
    );
  }

  Future<Map<String, dynamic>> doPrepareImageData(selectedImages,
      {String? filename, String? filetype}) async {
    String fname = filename ?? "";
    String ftype = filetype ?? "";
    String fdata = "";

    if (selectedImages != null && selectedImages.isNotEmpty) {
      // Get selected image
      final imageFile = selectedImages[0];

      // print(imageFile);
      if (imageFile != null) {
        if (imageFile.runtimeType != Uint8List) {
          fname = imageFile.name;
          ftype = imageFile.mimeType!;

          // 画像をBase64にエンコード
          await imageFile.readAsBytes().then((bytes) {
            fdata = base64Encode(bytes);
            // debugPrint("Base64 Image: $f64data");
          }).catchError((e) {
            debugPrint("Error reading image file: $e");
          });
        } else {
          fdata = base64Encode(imageFile);
        }
      }
    }
    return {
      "fname": fname,
      "ftype": ftype,
      "fdata": fdata,
    };
  }

  Future<Map<String, dynamic>> doPrepareFileData(
      List<PlatformFile>? selectedFiles,
      {String? filename,
      String? filetype}) async {
    String fname = filename ?? "";
    String? ftype = filetype ?? "";
    String fdata = "";

    /// 送信時に Base64 を取りたい場合:
    final payload =
        selectedFiles!.map((f) => CustomFileField.toBase64Record(f)).toList();
    if (payload.isNotEmpty) {
      fname = payload.first["name"] ?? "";
      ftype = payload.first["mime"] ?? "";
      fdata = payload.first["base64"] ?? "";
    }
    return {
      "fname": fname,
      "ftype": ftype,
      "fdata": fdata,
    };
  }
}

/// 拡張子推定（name → path → bytesからmime判定）
String? _extFromFile(PlatformFile f) {
  String extFromString(String s) {
    final i = s.lastIndexOf('.');
    return (i >= 0 && i < s.length - 1) ? s.substring(i + 1).toLowerCase() : '';
  }

  final fromName = extFromString(f.name);
  if (fromName.isNotEmpty) return fromName;

  final path = f.path ?? '';
  final fromPath = extFromString(path);
  if (fromPath.isNotEmpty) return fromPath;

  if (f.bytes != null) {
    final mimeType = lookupMimeType('', headerBytes: f.bytes);
    if (mimeType == 'application/pdf') return 'pdf';
    if (mimeType == 'image/png') return 'png';
    if (mimeType == 'image/jpeg') return 'jpg';
  }
  return null;
}

/// Base64 → PlatformFile 変換
PlatformFile platformFileFromBase64(
  String base64Data, {
  String? name,
}) {
  final bytes = base64Decode(base64Data);
  final mimeType = lookupMimeType('', headerBytes: bytes);
  String ext = 'bin';
  if (mimeType == 'application/pdf') ext = 'pdf';
  if (mimeType == 'image/png') ext = 'png';
  if (mimeType == 'image/jpeg') ext = 'jpg';

  return PlatformFile(
    name: name ?? "file_from_base64.$ext",
    size: bytes.length,
    bytes: bytes,
  );
}
