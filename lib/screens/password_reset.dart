import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:go_router/go_router.dart';
import 'package:koushuu_system/providers/appstate_provider.dart';
import 'package:koushuu_system/repositories/user_repository.dart';
import 'package:koushuu_system/widgets/bottom_bar.dart';
import 'package:koushuu_system/widgets/core_button.dart';
import 'package:koushuu_system/widgets/custom_text_field.dart';
import 'package:koushuu_system/widgets/default_text.dart';
import 'package:koushuu_system/widgets/top_bar.dart';
import 'package:koushuu_system/widgets/up_fab.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:koushuu_system/core/constants.dart' as constants;

class PasswordResetPage extends ConsumerWidget {
  PasswordResetPage({
    super.key,
    required this.title,
  });

  final String title;

  final ScrollController _scrollController = ScrollController();
  final _formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    //final step = ref.read(appStateProvider).step;
    String applicationId = "";
    SessionManager().get('application_id').then((a) {
      applicationId = a.toString();
    });
    return Scaffold(
        body: FormBuilder(
          key: _formKey,
          child: SingleChildScrollView(
            controller: _scrollController,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Row(
                  children: [
                    TopBar(
                      title: title,
                    ),
                  ],
                ),
                const SizedBox(
                  height: 30,
                ),
                const Padding(
                  padding: EdgeInsets.only(left: 20.0, right: 20.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: DefaultText(
                            fontSize: 12,
                            txt:
                                '登録済みのメールアドレスを入力して、送信ボタンをクリックしてください。パスワードリセットURLを送信します。'),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 20.0, right: 20.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            // Form Section
                            _buildTextField(
                              "email",
                              'メールアドレス',
                              false,
                              FormBuilderValidators.compose([
                                FormBuilderValidators.required(),
                                FormBuilderValidators.email(),
                              ]),
                              'anzen@◯△x.co.jp',
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CoreButton(
                      onPressed: () async {
                        //if (_formKey.currentState?.validate() ?? false) {
                        if (_formKey.currentState!.saveAndValidate()) {
                          Map<String, dynamic> formValues =
                              _formKey.currentState!.value;
                          String email = formValues["email"];

                          context.loaderOverlay.show();
                          try {
                            final res = await ref
                                .read(userRepositoryProvider)
                                .resetPassword(email, int.parse(applicationId));

                            context.loaderOverlay.hide();

                            if (res.data["status"] ==
                                constants.API_RES_SUCCESS) {
                              GoRouter.of(context).go('/login');
                            } else {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(res.data["message"]),
                                ),
                              );
                            }
                          } catch (e) {
                            context.loaderOverlay.hide();
                            print(e);
                          }
                        }
                      },
                      bgColor: Colors.red,
                      fgColor: Colors.white,
                      title: '送信',
                    ),
                    const SizedBox(
                      width: 20,
                    ),
                    CoreButton(
                      onPressed: () {
                        GoRouter.of(context).go('/');
                      },
                      bgColor: Colors.red,
                      fgColor: Colors.white,
                      title: 'キャンセル',
                    ),
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
              ],
            ),
          ),
        ),
        floatingActionButton: UpFabButton(
          scrollViewController: _scrollController,
        ),
        bottomNavigationBar: const BottomBar(title: ''));
  }
}

Widget _buildTextField(String name, String label, bool isSecure,
    String? Function(String?) validator, String? hintText) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 5.0),
    child: FormBuilderTextField(
      name: name,
      obscureText: isSecure,
      // controller: controller,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        hintText: hintText,
        floatingLabelBehavior: FloatingLabelBehavior.always,
      ),
      validator: validator,
    ),
  );
}
