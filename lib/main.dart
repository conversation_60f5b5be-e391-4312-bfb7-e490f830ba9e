import 'package:flutter/material.dart';
// import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_web_plugins/url_strategy.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:koushuu_system/route/routes.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:loader_overlay/loader_overlay.dart';

void main() async {
  usePathUrlStrategy();
  // const env = String.fromEnvironment('ENV', defaultValue: 'dev');
  // //await dotenv.load(fileName: ".env");
  // await dotenv.load(fileName: '.env.$env');
  runApp(
    const ProviderScope(
      child: <PERSON>ushuu<PERSON><PERSON>(),
    ),
  );
}

class KoushuuApp extends StatelessWidget {
  const KoushuuApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GlobalLoaderOverlay(
      child: MaterialApp.router(
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
          FormBuilderLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('ja'),
        ],
        //locale: const Locale('ja'),
        debugShowCheckedModeBanner: false,
        title: "講習Web予約",
        //title: AppLocalizations.of(context)!.systemTitle,
        theme: ThemeData(
          colorScheme:
              ColorScheme.fromSeed(seedColor: Colors.greenAccent.shade700),
          useMaterial3: true,
          textTheme: GoogleFonts.notoSansJpTextTheme(
            Theme.of(context).textTheme,
          ),
        ),
        routerConfig: router,
      ),
    );
  }
}
