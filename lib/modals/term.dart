import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:koushuu_system/widgets/bottom_bar.dart';
import 'package:koushuu_system/widgets/core_button.dart';
import 'package:koushuu_system/widgets/top_bar.dart';
import 'package:koushuu_system/widgets/up_fab.dart';

class TermPage extends ConsumerWidget {
  TermPage({
    super.key,
  });

  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
        body: SingleChildScrollView(
          controller: _scrollController,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              const Row(
                children: [
                  TopBar(
                    title: "プライバシーポリシー",
                    showCloseButton: false,
                  ),
                ],
              ),
              Container(
                margin: const EdgeInsets.all(10.0),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.transparent),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    HtmlWidget('''
<p>公益社団法人ポイラ・クレーン安全協会（以下「協会」といいます）は、下記「個人情報保護方針」に基づき、個人情報を慎重に取扱い、安全かつ適切な保護に努めてまいります。</p>
<h3>個人情報保護方針</h3>
<h3>個人情報の保護に関する法律及び関係法令・規則等の遵守</h3>
<p>当協会は個人情報に関する取扱いについて、関係法令・規則等を遵守します。</p>

<h3>個人情報の収集・利用に関する基本方針</h3>
<p>当協会は、個人情報の収集に際し、取引目的を正当な事業の範囲内で、その目的達成に必要な限度において、適法かつ公正な方法で取得し、利用します。<br>
また、利用目的の定めがある場合、あらかじめ他の正当な理由がある場合を除き、個人情報を第三者に提供することはありません。</p>

<h3>安全確保の措置</h3>
<p>当協会は個人情報の不正取得、漏えい、滅失又はき損の防止その他個人情報の安全管理のため必要かつ適切な措置を講じます。</p>

<h3>個人情報の確認・訂正等</h3>
<p>当協会は、ご本人様から、ご自身の個人情報の確認、訂正、利用停止等の請求があった場合には、すみやかに調査を行い、適切に対応します。</p>

<h3>＜ネット受付に関する規約＞</h3>
<h3>個人情報の範囲</h3>
<p>申込者様の氏名・会社名・生年月日・住所・電話番号・Eメールアドレスなどがあります。</p>

<h3>対象とする個人情報の使用目的</h3>
<p>お客様よりお預かりしました「個人情報」は、主として以下の目的のために使用させて頂きます。</p>
<ul>
  <li>各講習会における受講票の作成</li>
  <li>各講習会における受講者台帳・修了者台帳の作成</li>
  <li>各講習会における修了証の作成及び再交付処理</li>
  <li>各講習会に関するお問い合わせに関し必要な場合</li>
  <li>各講習の不備などお客様への連絡が必要な場合</li>
  <li>各講習会の募集を行う場合や、重要な伝達事項のDM送付</li>
</ul>

<h3>お客様の個人情報の第三者への開示・非提供</h3>
<p>お客様からお預かりしました「個人情報」は、以下の場合を除き、第三者への開示または提供をいたしません。</p>
<ul>
  <li>本人の同意がある場合</li>
  <li>法令に基づき開示</li>
  <li>提供を求められた場合</li>
</ul>'''),
                  ],
                ),
              ),

              const SizedBox(height: 20),
              // Buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CoreButton(
                    onPressed: () {
                      //GoRouter.of(context).pop(false);
                      GoRouter.of(context).go('/');
                    },
                    bgColor: Colors.grey,
                    fgColor: Colors.white,
                    title: 'キャンセル',
                  ),
                  const SizedBox(width: 20),
                  CoreButton(
                    onPressed: () async {
                      GoRouter.of(context).pop(true);
                    },
                    bgColor: Colors.red,
                    fgColor: Colors.white,
                    title: '同意',
                  ),
                ],
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
        floatingActionButton: UpFabButton(
          scrollViewController: _scrollController,
        ),
        bottomNavigationBar: const BottomBar(title: ''));
  }
}
