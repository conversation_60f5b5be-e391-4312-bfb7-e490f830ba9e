
rm -Rf ./build/web/*

flutter build web --release --dart-define-from-file=env/prd.json

#rsync --delete -ra ./build/web/* <EMAIL>:/var/www/websystem/
rsync  -ravi --verbose ./build/web/* <EMAIL>:/var/www/websystem/

#scp -r ./build/web/* <EMAIL>:/home/<USER>/websystem/
#scp -rp ./build/web/* <EMAIL>:/var/www/websystem/


#RESTART PM2

#sudo chown -R ubuntu:www-data /var/www/websystem
#sudo chmod -R 750 /var/www/websystem