# Design Document

## Overview

講習登録ページにおける画像・PDFファイルアップロード機能の拡張設計です。現在の`FormBuilderImagePicker`ベースの実装を、画像とPDFの両方に対応した`FormBuilderFilePicker`ベースの実装に拡張します。既存の`file_picker_with_pdf.dart`ウィジェットを基盤として、統一されたファイルアップロードUIを提供します。

## Architecture

### Component Structure

```
lib/widgets/
├── enhanced_file_picker.dart          # 新規：統合ファイルピッカーウィジェット
├── file_preview_widget.dart           # 新規：ファイルプレビュー表示ウィジェット
└── file_picker_with_pdf.dart          # 既存：参考実装（更新予定）

lib/screens/
└── reg_participant.dart               # 更新：_buildPhotoFieldを新ウィジェット使用に変更

lib/core/
└── file_utils.dart                    # 新規：ファイル処理ユーティリティ
```

### Data Flow

1. **ファイル選択**: ユーザーがファイル選択ボタンをクリック
2. **ファイル検証**: 形式・サイズ・内容の検証
3. **プレビュー表示**: 画像はサムネイル、PDFはアイコン表示
4. **Base64変換**: 選択されたファイルをBase64エンコード
5. **状態管理**: ファイル名、タイプ、Base64データを保存

## Components and Interfaces

### 1. EnhancedFilePicker Widget

```dart
class EnhancedFilePicker extends StatefulWidget {
  final String name;
  final String label;
  final bool isRequired;
  final List<String> allowedExtensions;
  final int maxFileSizeMB;
  final String? initialBase64Data;
  final String? initialFileName;
  final String? initialFileType;
  final Function(FileUploadData)? onFileSelected;
  final Function()? onFileRemoved;
}

class FileUploadData {
  final String fileName;
  final String fileType;
  final String base64Data;
  final int fileSizeBytes;
}
```

### 2. FilePreviewWidget

```dart
class FilePreviewWidget extends StatelessWidget {
  final FileUploadData fileData;
  final VoidCallback? onRemove;
  final VoidCallback? onReplace;
  final bool showControls;
}
```

### 3. FileUtils Utility Class

```dart
class FileUtils {
  static const List<String> supportedImageExtensions = ['jpg', 'jpeg', 'png'];
  static const List<String> supportedDocumentExtensions = ['pdf'];
  static const int maxFileSizeMB = 10;
  
  static Future<FileUploadData?> processFile(PlatformFile file);
  static bool isValidFileType(String fileName);
  static bool isValidFileSize(int sizeBytes);
  static String getFileExtension(String fileName);
  static bool isImageFile(String fileName);
  static bool isPdfFile(String fileName);
}
```

## Data Models

### FileUploadData Model

```dart
class FileUploadData {
  final String fileName;
  final String fileType;
  final String base64Data;
  final int fileSizeBytes;
  final DateTime uploadedAt;
  
  const FileUploadData({
    required this.fileName,
    required this.fileType,
    required this.base64Data,
    required this.fileSizeBytes,
    required this.uploadedAt,
  });
  
  // JSON serialization methods
  Map<String, dynamic> toJson();
  factory FileUploadData.fromJson(Map<String, dynamic> json);
  
  // Validation methods
  bool get isValid;
  bool get isImage;
  bool get isPdf;
  String get fileExtension;
  double get fileSizeMB;
}
```

### Enhanced Participant Model Updates

既存の`Participant`モデルは変更せず、新しいファイルアップロード機能は既存のフィールド構造と互換性を保ちます：

- `image_*` フィールド: Base64データ
- `filename_*` フィールド: ファイル名
- `filetype_*` フィールド: MIMEタイプ

## Error Handling

### Validation Errors

```dart
enum FileUploadError {
  invalidFileType,
  fileSizeExceeded,
  fileCorrupted,
  networkError,
  unknownError,
}

class FileUploadException implements Exception {
  final FileUploadError errorType;
  final String message;
  final String? fileName;
  
  const FileUploadException(this.errorType, this.message, [this.fileName]);
}
```

### Error Handling Strategy

1. **ファイル形式エラー**: 「対応していないファイル形式です。JPG、PNG、PDFファイルを選択してください。」
2. **ファイルサイズエラー**: 「ファイルサイズが10MBを超えています。より小さなファイルを選択してください。」
3. **ファイル破損エラー**: 「ファイルが破損している可能性があります。別のファイルを選択してください。」
4. **ネットワークエラー**: 「ネットワークエラーが発生しました。再度お試しください。」

### User Feedback

- **成功**: スナックバーまたはインラインメッセージで成功を通知
- **エラー**: 赤色のエラーメッセージを表示
- **進行状況**: ファイル処理中はローディングインジケーターを表示

## Testing Strategy

### Unit Tests

1. **FileUtils Tests**
   - ファイル形式検証テスト
   - ファイルサイズ検証テスト
   - Base64変換テスト
   - エラーハンドリングテスト

2. **FileUploadData Tests**
   - JSON serialization/deserialization テスト
   - バリデーションロジックテスト
   - プロパティアクセステスト

### Widget Tests

1. **EnhancedFilePicker Tests**
   - ファイル選択UIテスト
   - バリデーションメッセージ表示テスト
   - コールバック実行テスト

2. **FilePreviewWidget Tests**
   - 画像プレビュー表示テスト
   - PDFアイコン表示テスト
   - 削除・置換ボタンテスト

### Integration Tests

1. **End-to-End File Upload Flow**
   - 画像ファイル選択から保存まで
   - PDFファイル選択から保存まで
   - エラーケースのハンドリング

2. **Backward Compatibility Tests**
   - 既存のParticipantデータとの互換性
   - 既存のAPIとの互換性

## Implementation Phases

### Phase 1: Core Infrastructure
- `FileUtils`クラスの実装
- `FileUploadData`モデルの実装
- 基本的なファイル処理ロジック

### Phase 2: UI Components
- `EnhancedFilePicker`ウィジェットの実装
- `FilePreviewWidget`ウィジェットの実装
- エラーハンドリングUI

### Phase 3: Integration
- `reg_participant.dart`の`_buildPhotoField`更新
- 既存コードとの統合
- バックワード互換性の確保

### Phase 4: Testing & Polish
- 単体テスト・ウィジェットテストの実装
- エラーメッセージの日本語化
- パフォーマンス最適化

## Security Considerations

1. **ファイル検証**: 拡張子だけでなく、ファイルヘッダーも検証
2. **サイズ制限**: 10MB制限を厳格に適用
3. **Base64エンコーディング**: 安全なエンコーディング処理
4. **メモリ管理**: 大きなファイル処理時のメモリリーク防止

## Performance Considerations

1. **遅延読み込み**: ファイルプレビューの遅延生成
2. **メモリ効率**: Base64変換時のメモリ使用量最適化
3. **UI応答性**: ファイル処理中のUI凍結防止
4. **キャッシュ**: プレビュー画像のキャッシュ機能