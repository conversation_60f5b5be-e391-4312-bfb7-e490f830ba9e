# Requirements Document

## Introduction

講習登録ページ（reg_participant.dart）において、現在は画像ファイルのみのアップロード機能が実装されていますが、PDFファイルも同時にアップロード可能にする機能拡張を行います。これにより、ユーザーは本人写真、本人確認書類、資格証明書などを画像形式またはPDF形式で提出できるようになり、利便性が向上します。

## Requirements

### Requirement 1

**User Story:** As a 講習申込者, I want to upload both image files and PDF files for my documents, so that I can submit my documents in the most convenient format available.

#### Acceptance Criteria

1. WHEN ユーザーがファイル選択ボタンをクリック THEN システム SHALL 画像ファイル（JPG, JPEG, PNG）とPDFファイルの両方を選択可能にする
2. WHEN ユーザーがPDFファイルを選択 THEN システム SHALL PDFアイコンとファイル名を表示する
3. WHEN ユーザーが画像ファイルを選択 THEN システム SHALL 画像のプレビューを表示する
4. WHEN ユーザーがファイルを選択 THEN システム SHALL ファイルサイズ制限（10MB以下）をチェックする

### Requirement 2

**User Story:** As a 講習申込者, I want to see clear visual feedback for uploaded files, so that I can confirm what files I have selected before submitting.

#### Acceptance Criteria

1. WHEN PDFファイルがアップロード THEN システム SHALL PDFアイコンとファイル名を表示する
2. WHEN 画像ファイルがアップロード THEN システム SHALL サムネイル画像を表示する
3. WHEN ファイルがアップロード済み THEN システム SHALL 削除ボタンを表示する
4. WHEN ユーザーが削除ボタンをクリック THEN システム SHALL ファイルを削除し表示を更新する

### Requirement 3

**User Story:** As a システム管理者, I want uploaded files to be properly validated and converted to base64 format, so that the system can handle both image and PDF files consistently.

#### Acceptance Criteria

1. WHEN ファイルがアップロード THEN システム SHALL ファイル形式を検証する（JPG, JPEG, PNG, PDF のみ許可）
2. WHEN ファイルがアップロード THEN システム SHALL ファイルサイズを検証する（10MB以下）
3. WHEN ファイルが検証を通過 THEN システム SHALL ファイルをbase64形式に変換する
4. WHEN base64変換が完了 THEN システム SHALL ファイル名、ファイルタイプ、base64データを保存する

### Requirement 4

**User Story:** As a 講習申込者, I want the file upload interface to be intuitive and consistent across all document types, so that I can easily understand how to upload my documents.

#### Acceptance Criteria

1. WHEN ページが読み込まれ THEN システム SHALL 各ドキュメントタイプに対して統一されたファイルアップロードUIを表示する
2. WHEN ファイルが未選択 THEN システム SHALL 「ファイルを選択」ボタンを表示する
3. WHEN ファイルが選択済み THEN システム SHALL ファイルプレビューと「変更」「削除」ボタンを表示する
4. WHEN エラーが発生 THEN システム SHALL 分かりやすいエラーメッセージを表示する

### Requirement 5

**User Story:** As a システム開発者, I want the file upload functionality to be reusable and maintainable, so that it can be easily extended or modified in the future.

#### Acceptance Criteria

1. WHEN ファイルアップロード機能を実装 THEN システム SHALL 再利用可能なウィジェットとして作成する
2. WHEN ウィジェットを作成 THEN システム SHALL 設定可能なプロパティ（許可ファイル形式、最大サイズ、必須/任意など）を提供する
3. WHEN ファイル処理ロジックを実装 THEN システム SHALL エラーハンドリングとログ出力を含める
4. WHEN 既存のphoto_fieldウィジェットを更新 THEN システム SHALL 後方互換性を維持する