# Implementation Plan

- [ ] 1. Create core file processing utilities
  - Implement FileUtils class with file validation, size checking, and Base64 conversion methods
  - Create comprehensive error handling for different file types and validation scenarios
  - Add unit tests for all FileUtils methods to ensure reliability
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

- [ ] 2. Implement FileUploadData model
  - Create FileUploadData class with all required properties and methods
  - Implement JSON serialization and deserialization methods for data persistence
  - Add validation methods and computed properties for file type checking
  - Write unit tests for model functionality and edge cases
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

- [ ] 3. Build FilePreviewWidget component
  - Create widget to display file previews with different layouts for images and PDFs
  - Implement image thumbnail generation and PDF icon display
  - Add remove and replace action buttons with proper callback handling
  - Write widget tests for different file types and user interactions
  - _Requirements: 2.1, 2.2, 2.3, 2.4_

- [ ] 4. Develop EnhancedFilePicker widget
  - Create main file picker widget that supports both images and PDFs
  - Implement file selection UI with proper validation and error messaging
  - Add integration with FilePreviewWidget for selected file display
  - Include loading states and progress indicators for file processing
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 4.1, 4.2, 4.3, 4.4_

- [ ] 5. Update reg_participant.dart integration
  - Replace existing _buildPhotoField method to use EnhancedFilePicker widget
  - Ensure backward compatibility with existing Participant model structure
  - Update file processing logic to handle both image and PDF files
  - Maintain existing base64 encoding format for API compatibility
  - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [ ] 6. Implement comprehensive error handling
  - Add user-friendly error messages in Japanese for all validation scenarios
  - Create proper error display components with clear instructions
  - Implement retry mechanisms for recoverable errors
  - Add logging for debugging and monitoring purposes
  - _Requirements: 4.4, 3.1, 3.2, 3.3, 3.4_

- [ ] 7. Create integration tests
  - Write end-to-end tests for complete file upload workflow
  - Test both image and PDF file selection and processing
  - Verify backward compatibility with existing participant data
  - Test error scenarios and recovery mechanisms
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 2.1, 2.2, 2.3, 2.4_

- [ ] 8. Performance optimization and final polish
  - Optimize memory usage during file processing and Base64 conversion
  - Implement lazy loading for file previews to improve UI responsiveness
  - Add file size compression for large images while maintaining quality
  - Conduct final testing and code review for production readiness
  - _Requirements: 5.1, 5.2, 5.3, 5.4_